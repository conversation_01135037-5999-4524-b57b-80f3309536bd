import React from 'react';
import { Card, Typography } from 'antd';
import TestUpload from '../components/TestUpload/TestUpload';

const { Title } = Typography;

const TestUploadPage = () => {
  return (
    <div style={{ padding: 24, background: '#f0f2f5', minHeight: '100vh' }}>
      <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
        🧪 Test Upload Ảnh với Cloudinary
      </Title>
      
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        <TestUpload />
        
        <Card title="📝 Hướng dẫn test" style={{ marginTop: 24 }}>
          <div style={{ lineHeight: 1.8 }}>
            <h4>🔧 Cách test:</h4>
            <ol>
              <li><strong>Upload ảnh:</strong> Click vào khu vực upload và chọn ảnh từ máy tính</li>
              <li><strong>Kiểm tra upload:</strong> Ảnh sẽ được upload lên <PERSON>inary và hiển thị URL</li>
              <li><strong>Test selector:</strong> Click "Mở Image Selector" để test chọn ảnh</li>
              <li><strong>Xem thông tin:</strong> Kiểm tra JSON response để thấy thông tin ảnh đã upload</li>
            </ol>
            
            <h4>✅ Những gì cần kiểm tra:</h4>
            <ul>
              <li>Upload ảnh thành công lên Cloudinary</li>
              <li>Có URL gốc, thumbnail và optimized URL</li>
              <li>Image Selector hoạt động đúng</li>
              <li>Có thể chọn và hiển thị ảnh đã chọn</li>
            </ul>
            
            <h4>🚨 Lưu ý:</h4>
            <ul>
              <li>Đảm bảo backend đang chạy (npm run dev)</li>
              <li>Kiểm tra API key Cloudinary trong .env</li>
              <li>Chỉ upload file ảnh (jpg, png, gif, webp)</li>
              <li>Kích thước file tối đa 5MB</li>
            </ul>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TestUploadPage;
