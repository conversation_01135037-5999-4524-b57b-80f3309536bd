import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Form,
  Typography,
  Space,
  Divider,
  Table,
  InputNumber,
  Tag,
  AutoComplete,
  Checkbox,
  message,
  Empty,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import { useCustomers } from '../../hooks/useCustomers';
import { useAllProductVariants } from '../../hooks/useProducts';
import { useCreateOrder } from '../../hooks/useOrders';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateOrder = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // States
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [discountType, setDiscountType] = useState('amount'); // 'amount' or 'percent'
  const [isStockCheck, setIsStockCheck] = useState(true);

  // API hooks
  const { data: customers = [], isLoading: customersLoading } = useCustomers({
    search: customerSearch,
    limit: 20
  });

  const { data: productVariants = [], isLoading: productsLoading } = useAllProductVariants({
    search: productSearch,
    limit: 50
  });

  const createOrderMutation = useCreateOrder();

  // Calculate totals
  const subtotal = selectedProducts.reduce((sum, item) => 
    sum + (item.so_luong * item.gia_ban), 0
  );
  
  const discountAmount = form.getFieldValue('giam_gia') || 0;
  const finalDiscount = discountType === 'percent' 
    ? (subtotal * discountAmount / 100)
    : discountAmount;
  
  const total = subtotal - finalDiscount;

  // Handle customer selection
  const handleCustomerSelect = (value, option) => {
    const customer = customers.find(c => c.id === value);
    setSelectedCustomer(customer);
    form.setFieldsValue({
      khach_hang_id: customer?.id,
      ten_khach_hang: customer?.ten,
      so_dien_thoai: customer?.so_dien_thoai,
      dia_chi: customer?.dia_chi
    });
  };

  // Handle product selection
  const handleProductSelect = (productId) => {
    const product = productVariants.find(p => p.id === productId);
    if (!product) return;

    // Check if product already exists
    const existingIndex = selectedProducts.findIndex(p => p.id === productId);
    if (existingIndex >= 0) {
      // Increase quantity
      const newProducts = [...selectedProducts];
      newProducts[existingIndex].so_luong += 1;
      setSelectedProducts(newProducts);
    } else {
      // Add new product
      const newProduct = {
        id: product.id,
        ten_san_pham: product.ten_san_pham,
        ten_phien_ban: product.ten_phien_ban,
        ma_sku: product.ma_sku,
        anh: product.anh,
        gia_ban: product.gia_ban_le || 0,
        so_luong: 1,
        ton_kho: product.ton_kho || 0
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    }
    setProductSearch('');
  };

  // Handle quantity change
  const handleQuantityChange = (productId, quantity) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, so_luong: quantity || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Handle price change
  const handlePriceChange = (productId, price) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, gia_ban: price || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Remove product
  const handleRemoveProduct = (productId) => {
    setSelectedProducts(selectedProducts.filter(p => p.id !== productId));
  };

  // Handle form submit
  const handleSubmit = async (values) => {
    try {
      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      const orderData = {
        ...values,
        khach_hang_id: selectedCustomer?.id,
        chi_tiet_don_hang: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong: product.so_luong,
          gia_ban: product.gia_ban,
          thanh_tien: product.so_luong * product.gia_ban
        })),
        tong_tien_hang: subtotal,
        giam_gia: finalDiscount,
        tong_thanh_toan: total,
        loai_giam_gia: discountType
      };

      console.log('Order data:', orderData);

      await createOrderMutation.mutateAsync(orderData);
      navigate('/orders');
    } catch (error) {
      console.error('Error creating order:', error);
    }
  };

  // Product table columns
  const productColumns = [
    {
      title: 'STT',
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <img 
            src={record.anh || '/placeholder-image.png'} 
            alt={record.ten_san_pham}
            style={{ width: 40, height: 40, objectFit: 'cover', borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.ten_phien_ban} - {record.ma_sku}
            </Text>
            {isStockCheck && (
              <div>
                <Text type="secondary" style={{ fontSize: 11 }}>
                  Tồn: {record.ton_kho}
                </Text>
                {record.so_luong > record.ton_kho && (
                  <Text type="danger" style={{ fontSize: 11, marginLeft: 8 }}>
                    Vượt tồn kho!
                  </Text>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Số lượng',
      key: 'so_luong',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <InputNumber
          min={1}
          value={record.so_luong}
          onChange={(value) => handleQuantityChange(record.id, value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn giá',
      key: 'gia_ban',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <InputNumber
          min={0}
          value={record.gia_ban}
          onChange={(value) => handlePriceChange(record.id, value)}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Thành tiền',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <Text strong>
          {(record.so_luong * record.gia_ban).toLocaleString('vi-VN')}đ
        </Text>
      )
    },
    {
      title: '',
      key: 'action',
      width: 50,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveProduct(record.id)}
          size="small"
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Tạo đơn hàng
        </Title>
        <Space>
          <Button onClick={() => navigate('/orders')}>
            Hủy
          </Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            disabled={selectedProducts.length === 0}
            loading={createOrderMutation.isLoading}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ngay_dat_hang: dayjs(),
          trang_thai: 'cho_xac_nhan',
          giam_gia: 0
        }}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Customer Information */}
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  Thông tin khách hàng
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Tìm kiếm khách hàng">
                    <AutoComplete
                      value={customerSearch}
                      onChange={setCustomerSearch}
                      onSelect={handleCustomerSelect}
                      placeholder="Tìm theo tên, SĐT, mã khách hàng..."
                      options={customers.map(customer => ({
                        value: customer.id,
                        label: (
                          <div>
                            <div>{customer.ten}</div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {customer.so_dien_thoai} - {customer.ma_khach_hang}
                            </Text>
                          </div>
                        )
                      }))}
                      filterOption={false}
                      loading={customersLoading}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="ten_khach_hang" label="Tên khách hàng">
                    <Input placeholder="Nhập tên khách hàng" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="so_dien_thoai" label="Số điện thoại">
                    <Input placeholder="Nhập số điện thoại" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="dia_chi" label="Địa chỉ">
                    <Input placeholder="Nhập địa chỉ" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Product Information */}
            <Card 
              title={
                <Space>
                  <ShoppingCartOutlined />
                  Thông tin sản phẩm
                </Space>
              }
              extra={
                <Space>
                  <Checkbox 
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Text type="secondary">Kiểm tra tồn kho</Text>
                </Space>
              }
            >
              <div style={{ marginBottom: 16 }}>
                <AutoComplete
                  value={productSearch}
                  onChange={setProductSearch}
                  onSelect={handleProductSelect}
                  placeholder="Tìm theo tên, mã SKU, mã vạch, Barcode... (F3)"
                  style={{ width: '100%' }}
                  options={productVariants.map(product => ({
                    value: product.id,
                    label: (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <img 
                          src={product.anh || '/placeholder-image.png'} 
                          alt={product.ten_san_pham}
                          style={{ width: 32, height: 32, objectFit: 'cover', borderRadius: 4 }}
                        />
                        <div>
                          <div>{product.ten_san_pham}</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {product.ten_phien_ban} - {product.ma_sku} - Tồn: {product.ton_kho || 0}
                          </Text>
                        </div>
                      </div>
                    )
                  }))}
                  filterOption={false}
                  loading={productsLoading}
                />
              </div>

              {selectedProducts.length === 0 ? (
                <Empty 
                  description="Chưa có thông tin sản phẩm"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={() => document.querySelector('.ant-select-selection-search-input').focus()}
                  >
                    Thêm sản phẩm
                  </Button>
                </Empty>
              ) : (
                <Table
                  columns={productColumns}
                  dataSource={selectedProducts}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  scroll={{ x: 800 }}
                />
              )}
            </Card>
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Order Summary */}
            <Card 
              title={
                <Space>
                  <DollarOutlined />
                  Thông tin bổ sung
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item name="ngay_dat_hang" label="Ngày đặt hàng">
                <DatePicker 
                  style={{ width: '100%' }} 
                  format="DD/MM/YYYY"
                />
              </Form.Item>

              <Form.Item name="trang_thai" label="Trạng thái">
                <Select>
                  <Option value="cho_xac_nhan">Chờ xác nhận</Option>
                  <Option value="da_xac_nhan">Đã xác nhận</Option>
                  <Option value="dang_giao">Đang giao</Option>
                  <Option value="hoan_thanh">Hoàn thành</Option>
                  <Option value="huy">Hủy</Option>
                </Select>
              </Form.Item>

              <Form.Item name="ghi_chu" label="Ghi chú đơn hàng">
                <TextArea 
                  rows={3} 
                  placeholder="Ghi chú thêm về đơn hàng"
                />
              </Form.Item>
            </Card>

            {/* Payment Summary */}
            <Card title="Đóng gói và giao hàng">
              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text>Tổng tiền ({selectedProducts.length} sản phẩm)</Text>
                  <Text strong>{subtotal.toLocaleString('vi-VN')}đ</Text>
                </Space>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Row gutter={8}>
                  <Col span={16}>
                    <Form.Item name="giam_gia" style={{ margin: 0 }}>
                      <InputNumber
                        placeholder="Giảm giá"
                        style={{ width: '100%' }}
                        min={0}
                        max={discountType === 'percent' ? 100 : subtotal}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Select
                      value={discountType}
                      onChange={setDiscountType}
                      style={{ width: '100%' }}
                    >
                      <Option value="amount">VNĐ</Option>
                      <Option value="percent">%</Option>
                    </Select>
                  </Col>
                </Row>
              </div>

              <Divider style={{ margin: '16px 0' }} />

              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>Khách phải trả</Text>
                  <Text strong style={{ fontSize: 16, color: '#1890ff' }}>
                    {total.toLocaleString('vi-VN')}đ
                  </Text>
                </Space>
              </div>

              <div style={{ marginTop: 16 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Bạn hãy thêm thông tin khách hàng để xử dụng được ưu đãi tại đây
                </Text>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default CreateOrder;
