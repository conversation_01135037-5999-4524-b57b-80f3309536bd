import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Form,
  Typography,
  Space,
  Divider,
  Table,
  InputNumber,
  Tag,
  AutoComplete,
  Checkbox,
  message,
  Empty,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import { useCustomers } from '../../hooks/useCustomers';
import { useAllProductVariants } from '../../hooks/useProducts';
import { useCreateOrder } from '../../hooks/useOrders';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateOrder = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // States
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [discountType, setDiscountType] = useState('amount'); // 'amount' or 'percent'
  const [isStockCheck, setIsStockCheck] = useState(true);
  const [customerDropdownOpen, setCustomerDropdownOpen] = useState(false);
  const [productDropdownOpen, setProductDropdownOpen] = useState(false);

  // API hooks
  const { data: customers = [], isLoading: customersLoading } = useCustomers({
    search: customerSearch,
    limit: 20
  });

  const { data: productVariants = [], isLoading: productsLoading } = useAllProductVariants({
    search: productSearch,
    limit: 50
  });

  const createOrderMutation = useCreateOrder();

  // Calculate totals
  const subtotal = selectedProducts.reduce((sum, item) => 
    sum + (item.so_luong * item.gia_ban), 0
  );
  
  const discountAmount = form.getFieldValue('giam_gia') || 0;
  const finalDiscount = discountType === 'percent' 
    ? (subtotal * discountAmount / 100)
    : discountAmount;
  
  const total = subtotal - finalDiscount;

  // Handle customer selection
  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    setCustomerSearch(customer?.ten || '');
    setCustomerDropdownOpen(false);
    form.setFieldsValue({
      khach_hang_id: customer?.id,
      ten_khach_hang: customer?.ten,
      so_dien_thoai: customer?.so_dien_thoai,
      dia_chi: customer?.dia_chi
    });
  };

  // Handle customer search change
  const handleCustomerSearchChange = (value) => {
    setCustomerSearch(value);
    if (!customerDropdownOpen) {
      setCustomerDropdownOpen(true);
    }
  };

  // Handle customer dropdown focus
  const handleCustomerFocus = () => {
    setCustomerDropdownOpen(true);
  };

  // Handle customer dropdown blur
  const handleCustomerBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setCustomerDropdownOpen(false);
    }, 200);
  };

  // Handle product selection
  const handleProductSelect = (product) => {
    if (!product) return;

    // Check if product already exists
    const existingIndex = selectedProducts.findIndex(p => p.id === product.id);
    if (existingIndex >= 0) {
      // Increase quantity
      const newProducts = [...selectedProducts];
      newProducts[existingIndex].so_luong += 1;
      setSelectedProducts(newProducts);
    } else {
      // Add new product
      const newProduct = {
        id: product.id,
        ten_san_pham: product.ten_san_pham,
        ten_phien_ban: product.ten_phien_ban,
        ma_sku: product.ma_sku,
        anh: product.anh,
        gia_ban: product.gia_ban_le || 0,
        so_luong: 1,
        ton_kho: product.ton_kho || 0
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    }
    setProductSearch('');
    setProductDropdownOpen(false);
  };

  // Handle product search change
  const handleProductSearchChange = (value) => {
    setProductSearch(value);
    if (!productDropdownOpen) {
      setProductDropdownOpen(true);
    }
  };

  // Handle product dropdown focus
  const handleProductFocus = () => {
    setProductDropdownOpen(true);
  };

  // Handle product dropdown blur
  const handleProductBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setProductDropdownOpen(false);
    }, 200);
  };

  // Handle quantity change
  const handleQuantityChange = (productId, quantity) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, so_luong: quantity || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Handle price change
  const handlePriceChange = (productId, price) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, gia_ban: price || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Remove product
  const handleRemoveProduct = (productId) => {
    setSelectedProducts(selectedProducts.filter(p => p.id !== productId));
  };

  // Handle form submit
  const handleSubmit = async (values) => {
    try {
      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      const orderData = {
        ...values,
        khach_hang_id: selectedCustomer?.id,
        chi_tiet_don_hang: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong: product.so_luong,
          gia_ban: product.gia_ban,
          thanh_tien: product.so_luong * product.gia_ban
        })),
        tong_tien_hang: subtotal,
        giam_gia: finalDiscount,
        tong_thanh_toan: total,
        loai_giam_gia: discountType
      };

      console.log('Order data:', orderData);

      await createOrderMutation.mutateAsync(orderData);
      navigate('/orders');
    } catch (error) {
      console.error('Error creating order:', error);
    }
  };

  // Product table columns
  const productColumns = [
    {
      title: 'STT',
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <img 
            src={record.anh || '/placeholder-image.png'} 
            alt={record.ten_san_pham}
            style={{ width: 40, height: 40, objectFit: 'cover', borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.ten_phien_ban} - {record.ma_sku}
            </Text>
            {isStockCheck && (
              <div>
                <Text type="secondary" style={{ fontSize: 11 }}>
                  Tồn: {record.ton_kho}
                </Text>
                {record.so_luong > record.ton_kho && (
                  <Text type="danger" style={{ fontSize: 11, marginLeft: 8 }}>
                    Vượt tồn kho!
                  </Text>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Số lượng',
      key: 'so_luong',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <InputNumber
          min={1}
          value={record.so_luong}
          onChange={(value) => handleQuantityChange(record.id, value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn giá',
      key: 'gia_ban',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <InputNumber
          min={0}
          value={record.gia_ban}
          onChange={(value) => handlePriceChange(record.id, value)}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Thành tiền',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <Text strong>
          {(record.so_luong * record.gia_ban).toLocaleString('vi-VN')}đ
        </Text>
      )
    },
    {
      title: '',
      key: 'action',
      width: 50,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveProduct(record.id)}
          size="small"
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Tạo đơn hàng
        </Title>
        <Space>
          <Button onClick={() => navigate('/orders')}>
            Hủy
          </Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            disabled={selectedProducts.length === 0}
            loading={createOrderMutation.isLoading}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ngay_dat_hang: dayjs(),
          trang_thai: 'cho_xac_nhan',
          giam_gia: 0
        }}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Customer Information */}
            <Card
              title="Thông tin khách hàng"
              style={{ marginBottom: 24 }}
            >
              <div style={{ position: 'relative' }}>
                <Input
                  value={customerSearch}
                  onChange={(e) => handleCustomerSearchChange(e.target.value)}
                  onFocus={handleCustomerFocus}
                  onBlur={handleCustomerBlur}
                  placeholder="Tìm theo tên, SĐT, mã khách hàng... (F4)"
                  prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                  style={{ marginBottom: 16 }}
                />

                {customerDropdownOpen && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    zIndex: 1000,
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {/* Add new customer button */}
                    <div
                      style={{
                        padding: '12px 16px',
                        borderBottom: '1px solid #f0f0f0',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 8,
                        color: '#1890ff'
                      }}
                      onClick={() => {
                        message.info('Tính năng thêm khách hàng mới sẽ được phát triển');
                        setCustomerDropdownOpen(false);
                      }}
                    >
                      <PlusOutlined />
                      <Text style={{ color: '#1890ff' }}>Thêm mới khách hàng</Text>
                    </div>

                    {/* Customer list */}
                    {customers
                      .filter(customer =>
                        customer.ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                        customer.so_dien_thoai?.includes(customerSearch) ||
                        customer.ma_khach_hang?.toLowerCase().includes(customerSearch.toLowerCase())
                      )
                      .map(customer => (
                        <div
                          key={customer.id}
                          style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 12,
                            ':hover': {
                              backgroundColor: '#f5f5f5'
                            }
                          }}
                          onClick={() => handleCustomerSelect(customer)}
                          onMouseEnter={(e) => {
                            e.target.style.backgroundColor = '#f5f5f5';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.backgroundColor = 'white';
                          }}
                        >
                          <div style={{
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            backgroundColor: '#1890ff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: 14,
                            fontWeight: 500
                          }}>
                            {customer.ten?.charAt(0)?.toUpperCase() || 'K'}
                          </div>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontWeight: 500, marginBottom: 2 }}>
                              {customer.ten || 'Khách hàng'}
                            </div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {customer.so_dien_thoai}
                            </Text>
                          </div>
                        </div>
                      ))}

                    {customers.filter(customer =>
                      customer.ten?.toLowerCase().includes(customerSearch.toLowerCase()) ||
                      customer.so_dien_thoai?.includes(customerSearch) ||
                      customer.ma_khach_hang?.toLowerCase().includes(customerSearch.toLowerCase())
                    ).length === 0 && customerSearch && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Không tìm thấy khách hàng</Text>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Selected customer info */}
              {selectedCustomer && (
                <div style={{
                  padding: '12px',
                  backgroundColor: '#f6f8fa',
                  borderRadius: '6px',
                  marginBottom: 16
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
                    <div style={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: 14,
                      fontWeight: 500
                    }}>
                      {selectedCustomer.ten?.charAt(0)?.toUpperCase() || 'K'}
                    </div>
                    <div>
                      <div style={{ fontWeight: 500 }}>{selectedCustomer.ten}</div>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {selectedCustomer.so_dien_thoai} - {selectedCustomer.ma_khach_hang}
                      </Text>
                    </div>
                  </div>
                  {selectedCustomer.dia_chi && (
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Địa chỉ: {selectedCustomer.dia_chi}
                    </Text>
                  )}
                </div>
              )}
            </Card>

            {/* Product Information */}
            <Card
              title="Thông tin sản phẩm"
              extra={
                <Space>
                  <Checkbox
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Button type="link" style={{ color: '#1890ff', fontSize: 12 }}>
                    Kiểm tra tồn kho
                  </Button>
                </Space>
              }
            >
              <div style={{ position: 'relative', marginBottom: 16 }}>
                <Input
                  value={productSearch}
                  onChange={(e) => handleProductSearchChange(e.target.value)}
                  onFocus={handleProductFocus}
                  onBlur={handleProductBlur}
                  placeholder="Tìm theo tên, mã SKU, mã vạch, Barcode... (F3)"
                  prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                />

                {productDropdownOpen && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    zIndex: 1000,
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {/* Product list */}
                    {productVariants
                      .filter(product =>
                        product.ten_san_pham?.toLowerCase().includes(productSearch.toLowerCase()) ||
                        product.ten_phien_ban?.toLowerCase().includes(productSearch.toLowerCase()) ||
                        product.ma_sku?.toLowerCase().includes(productSearch.toLowerCase())
                      )
                      .map(product => (
                        <div
                          key={product.id}
                          style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 12
                          }}
                          onClick={() => handleProductSelect(product)}
                          onMouseEnter={(e) => {
                            e.target.style.backgroundColor = '#f5f5f5';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.backgroundColor = 'white';
                          }}
                        >
                          <img
                            src={product.anh || '/placeholder-image.png'}
                            alt={product.ten_san_pham}
                            style={{
                              width: 40,
                              height: 40,
                              objectFit: 'cover',
                              borderRadius: 4,
                              border: '1px solid #f0f0f0'
                            }}
                          />
                          <div style={{ flex: 1 }}>
                            <div style={{ fontWeight: 500, marginBottom: 2 }}>
                              {product.ten_san_pham}
                            </div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {product.ten_phien_ban} - {product.ma_sku}
                            </Text>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 4 }}>
                              <Text style={{ fontSize: 12, color: '#1890ff' }}>
                                {product.gia_ban_le?.toLocaleString('vi-VN')}đ
                              </Text>
                              <Text style={{
                                fontSize: 12,
                                color: (product.ton_kho || 0) > 0 ? '#52c41a' : '#ff4d4f'
                              }}>
                                Tồn: {product.ton_kho || 0}
                              </Text>
                            </div>
                          </div>
                        </div>
                      ))}

                    {productVariants.filter(product =>
                      product.ten_san_pham?.toLowerCase().includes(productSearch.toLowerCase()) ||
                      product.ten_phien_ban?.toLowerCase().includes(productSearch.toLowerCase()) ||
                      product.ma_sku?.toLowerCase().includes(productSearch.toLowerCase())
                    ).length === 0 && productSearch && (
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">Không tìm thấy sản phẩm</Text>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Add product button */}
              {selectedProducts.length === 0 && (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Empty
                    description="Chưa có thông tin sản phẩm"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  >
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setProductDropdownOpen(true)}
                    >
                      Thêm sản phẩm
                    </Button>
                  </Empty>
                </div>
              )}

              {/* Selected products table */}
              {selectedProducts.length > 0 && (
                <Table
                  columns={productColumns}
                  dataSource={selectedProducts}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  scroll={{ x: 800 }}
                />
              )}
            </Card>
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Order Summary */}
            <Card 
              title={
                <Space>
                  <DollarOutlined />
                  Thông tin bổ sung
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item name="ngay_dat_hang" label="Ngày đặt hàng">
                <DatePicker 
                  style={{ width: '100%' }} 
                  format="DD/MM/YYYY"
                />
              </Form.Item>

              <Form.Item name="trang_thai" label="Trạng thái">
                <Select>
                  <Option value="cho_xac_nhan">Chờ xác nhận</Option>
                  <Option value="da_xac_nhan">Đã xác nhận</Option>
                  <Option value="dang_giao">Đang giao</Option>
                  <Option value="hoan_thanh">Hoàn thành</Option>
                  <Option value="huy">Hủy</Option>
                </Select>
              </Form.Item>

              <Form.Item name="ghi_chu" label="Ghi chú đơn hàng">
                <TextArea 
                  rows={3} 
                  placeholder="Ghi chú thêm về đơn hàng"
                />
              </Form.Item>
            </Card>

            {/* Pricing Summary */}
            <Card>
              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Checkbox
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Button type="link" style={{ color: '#1890ff', fontSize: 12, padding: 0 }}>
                    Kiểm tra tồn kho
                  </Button>
                </Space>
              </div>

              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}>
                  <Select defaultValue="F10" style={{ width: '100%' }}>
                    <Option value="F10">(F10)</Option>
                    <Option value="F11">(F11)</Option>
                  </Select>
                </Col>
                <Col span={12}>
                  <Select defaultValue="gia_ban_le" style={{ width: '100%' }}>
                    <Option value="gia_ban_le">Giá bán lẻ</Option>
                    <Option value="gia_ban_buon">Giá bán buôn</Option>
                  </Select>
                </Col>
              </Row>

              <Divider style={{ margin: '16px 0' }} />

              {/* Order totals */}
              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Tổng tiền ({selectedProducts.length} sản phẩm)</Col>
                  <Col>{subtotal.toLocaleString('vi-VN')}đ</Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Chiết khấu (F6)</Col>
                  <Col>0</Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Phí giao hàng (F7)</Col>
                  <Col>0</Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Mã giảm giá</Col>
                  <Col>0</Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Khách phải trả</Col>
                  <Col>{total.toLocaleString('vi-VN')}đ</Col>
                </Row>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Row justify="space-between">
                  <Col>Khách đã trả</Col>
                  <Col>0</Col>
                </Row>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default CreateOrder;
