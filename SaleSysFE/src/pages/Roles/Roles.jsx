import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Card,
  Typography,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Select,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';

import { rolesAPI, permissionsAPI } from '../../services/api';
import { usePermissions } from '../../contexts/PermissionContext';

const { Title, Text } = Typography;
const { TextArea } = Input;

const Roles = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [form] = Form.useForm();

  const { isAdmin } = usePermissions();
  const queryClient = useQueryClient();

  // Fetch roles
  const { data: rolesData, isLoading, refetch } = useQuery('roles', rolesAPI.getRoles);

  // Fetch permissions for form
  const { data: permissionsData } = useQuery('permissions', permissionsAPI.getPermissions);

  // Create role mutation
  const createRoleMutation = useMutation(rolesAPI.createRole, {
    onSuccess: () => {
      message.success('Tạo vai trò thành công!');
      setIsModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('roles');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo vai trò');
    }
  });

  // Update role mutation
  const updateRoleMutation = useMutation(
    ({ id, data }) => rolesAPI.updateRole(id, data),
    {
      onSuccess: () => {
        message.success('Cập nhật vai trò thành công!');
        setIsModalVisible(false);
        setEditingRole(null);
        form.resetFields();
        queryClient.invalidateQueries('roles');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật vai trò');
      }
    }
  );

  // Delete role mutation
  const deleteRoleMutation = useMutation(rolesAPI.deleteRole, {
    onSuccess: () => {
      message.success('Xóa vai trò thành công!');
      queryClient.invalidateQueries('roles');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa vai trò');
    }
  });

  const handleCreate = () => {
    setEditingRole(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingRole(record);
    form.setFieldsValue({
      ...record,
      quyen_ids: record.quyenList?.map(permission => permission.id) || []
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id) => {
    deleteRoleMutation.mutate(id);
  };

  const handleSubmit = async (values) => {
    try {
      if (editingRole) {
        await updateRoleMutation.mutateAsync({
          id: editingRole.id,
          data: values
        });
      } else {
        await createRoleMutation.mutateAsync(values);
      }
    } catch (error) {
      // Error handled in mutation
    }
  };

  const columns = [
    {
      title: 'Mã vai trò',
      dataIndex: 'ma_vai_tro',
      key: 'ma_vai_tro',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Tên vai trò',
      dataIndex: 'ten_vai_tro',
      key: 'ten_vai_tro',
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta',
      ellipsis: true,
    },
    {
      title: 'Số quyền',
      dataIndex: 'quyenList',
      key: 'quyenList',
      render: (permissions) => (
        <Tag color="green">{permissions?.length || 0} quyền</Tag>
      ),
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          {isAdmin() && (
            <>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                size="small"
              />
              <Popconfirm
                title="Bạn có chắc chắn muốn xóa vai trò này?"
                onConfirm={() => handleDelete(record.id)}
                okText="Có"
                cancelText="Không"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                />
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                Quản lý vai trò
              </Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                  loading={isLoading}
                >
                  Làm mới
                </Button>
                {isAdmin() && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleCreate}
                  >
                    Thêm vai trò
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={rolesData?.data?.data?.roles || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} vai trò`,
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingRole ? 'Chỉnh sửa vai trò' : 'Thêm vai trò mới'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingRole(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ma_vai_tro"
                label="Mã vai trò"
                rules={[{ required: true, message: 'Vui lòng nhập mã vai trò!' }]}
              >
                <Input 
                  placeholder="Nhập mã vai trò (VD: NHAN_VIEN)" 
                  style={{ textTransform: 'uppercase' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ten_vai_tro"
                label="Tên vai trò"
                rules={[{ required: true, message: 'Vui lòng nhập tên vai trò!' }]}
              >
                <Input placeholder="Nhập tên vai trò" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="mo_ta"
            label="Mô tả"
          >
            <TextArea 
              rows={3} 
              placeholder="Nhập mô tả vai trò" 
            />
          </Form.Item>

          <Form.Item
            name="quyen_ids"
            label="Quyền"
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {permissionsData?.data?.data?.permissions?.map(permission => (
                  <Col span={12} key={permission.id}>
                    <Checkbox value={permission.id}>
                      <Space direction="vertical" size={0}>
                        <Text strong>{permission.ten_quyen}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {permission.ma_quyen}
                        </Text>
                      </Space>
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createRoleMutation.isLoading || updateRoleMutation.isLoading}
              >
                {editingRole ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Roles;
