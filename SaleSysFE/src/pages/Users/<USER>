import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Card,
  Typography,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Avatar
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';

import { usersAPI, rolesAPI } from '../../services/api';
import { usePermissions } from '../../contexts/PermissionContext';

const { Title } = Typography;
const { Option } = Select;

const Users = () => {
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();

  const { canCreate, canEdit, canDelete } = usePermissions();
  const queryClient = useQueryClient();

  // Fetch users
  const { data: usersData, isLoading, refetch } = useQuery(
    ['users', currentPage, pageSize, searchText, filterType, filterStatus],
    () => usersAPI.getUsers({
      page: currentPage,
      limit: pageSize,
      search: searchText,
      loai_nguoi_dung: filterType,
      trang_thai: filterStatus
    }),
    {
      keepPreviousData: true
    }
  );

  // Fetch roles for form
  const { data: rolesData } = useQuery('roles', rolesAPI.getRoles);

  // Create user mutation
  const createUserMutation = useMutation(usersAPI.createUser, {
    onSuccess: () => {
      message.success('Tạo người dùng thành công!');
      setIsModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('users');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo người dùng');
    }
  });

  // Update user mutation
  const updateUserMutation = useMutation(
    ({ id, data }) => usersAPI.updateUser(id, data),
    {
      onSuccess: () => {
        message.success('Cập nhật người dùng thành công!');
        setIsModalVisible(false);
        setEditingUser(null);
        form.resetFields();
        queryClient.invalidateQueries('users');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật người dùng');
      }
    }
  );

  // Delete user mutation
  const deleteUserMutation = useMutation(usersAPI.deleteUser, {
    onSuccess: () => {
      message.success('Xóa người dùng thành công!');
      queryClient.invalidateQueries('users');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa người dùng');
    }
  });

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleFilterChange = (type, value) => {
    if (type === 'loai_nguoi_dung') {
      setFilterType(value);
    } else if (type === 'trang_thai') {
      setFilterStatus(value);
    }
    setCurrentPage(1);
  };

  const handleCreate = () => {
    setEditingUser(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingUser(record);
    form.setFieldsValue({
      ...record,
      vai_tro_ids: record.vaiTroList?.map(role => role.id) || []
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id) => {
    deleteUserMutation.mutate(id);
  };

  const handleSubmit = async (values) => {
    try {
      if (editingUser) {
        await updateUserMutation.mutateAsync({
          id: editingUser.id,
          data: values
        });
      } else {
        await createUserMutation.mutateAsync(values);
      }
    } catch (error) {
      // Error handled in mutation
    }
  };

  const columns = [
    {
      title: 'Avatar',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 60,
      render: (_, record) => (
        <Avatar 
          size="small" 
          icon={<UserOutlined />}
          style={{ backgroundColor: '#1890ff' }}
        >
          {record.ho_ten?.charAt(0)?.toUpperCase()}
        </Avatar>
      ),
    },
    {
      title: 'Họ tên',
      dataIndex: 'ho_ten',
      key: 'ho_ten',
      sorter: true,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'so_dien_thoai',
      key: 'so_dien_thoai',
    },
    {
      title: 'Loại người dùng',
      dataIndex: 'loai_nguoi_dung',
      key: 'loai_nguoi_dung',
      render: (type) => {
        const typeMap = {
          admin: { color: 'red', text: 'Quản trị viên' },
          nhan_vien: { color: 'blue', text: 'Nhân viên' },
          khach_hang: { color: 'green', text: 'Khách hàng' },
        };
        const { color, text } = typeMap[type] || { color: 'default', text: type };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Vai trò',
      dataIndex: 'vaiTroList',
      key: 'vaiTroList',
      render: (roles) => (
        <Space wrap>
          {roles?.map(role => (
            <Tag key={role.id} color="purple">
              {role.ten_vai_tro}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      render: (status) => {
        const statusMap = {
          dang_giao_dich: { color: 'green', text: 'Đang giao dịch' },
          ngung_giao_dich: { color: 'red', text: 'Ngừng giao dịch' },
          tam_khoa: { color: 'orange', text: 'Tạm khóa' },
        };
        const { color, text } = statusMap[status] || { color: 'default', text: status };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          {canEdit('NGUOI_DUNG') && (
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          )}
          {canDelete('NGUOI_DUNG') && (
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa người dùng này?"
              onConfirm={() => handleDelete(record.id)}
              okText="Có"
              cancelText="Không"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                Quản lý người dùng
              </Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                  loading={isLoading}
                >
                  Làm mới
                </Button>
                {canCreate('NGUOI_DUNG') && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleCreate}
                  >
                    Thêm người dùng
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </div>

        {/* Filters */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={8}>
            <Input.Search
              placeholder="Tìm kiếm theo tên, email, số điện thoại..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={8}>
            <Select
              placeholder="Lọc theo loại người dùng"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('loai_nguoi_dung', value)}
            >
              <Option value="admin">Quản trị viên</Option>
              <Option value="nhan_vien">Nhân viên</Option>
              <Option value="khach_hang">Khách hàng</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <Select
              placeholder="Lọc theo trạng thái"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('trang_thai', value)}
            >
              <Option value="dang_giao_dich">Đang giao dịch</Option>
              <Option value="ngung_giao_dich">Ngừng giao dịch</Option>
              <Option value="tam_khoa">Tạm khóa</Option>
            </Select>
          </Col>
        </Row>

        {/* Table */}
        <Table
          columns={columns}
          dataSource={usersData?.data?.data?.users || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: usersData?.data?.data?.pagination?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} người dùng`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size);
            },
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingUser ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ho_ten"
                label="Họ tên"
                rules={[{ required: true, message: 'Vui lòng nhập họ tên!' }]}
              >
                <Input placeholder="Nhập họ tên" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Vui lòng nhập email!' },
                  { type: 'email', message: 'Email không hợp lệ!' }
                ]}
              >
                <Input placeholder="Nhập email" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="so_dien_thoai"
                label="Số điện thoại"
              >
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="loai_nguoi_dung"
                label="Loại người dùng"
                rules={[{ required: true, message: 'Vui lòng chọn loại người dùng!' }]}
              >
                <Select placeholder="Chọn loại người dùng">
                  <Option value="admin">Quản trị viên</Option>
                  <Option value="nhan_vien">Nhân viên</Option>
                  <Option value="khach_hang">Khách hàng</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Form.Item
              name="mat_khau"
              label="Mật khẩu"
              rules={[
                { required: true, message: 'Vui lòng nhập mật khẩu!' },
                { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự!' }
              ]}
            >
              <Input.Password placeholder="Nhập mật khẩu" />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="trang_thai"
                label="Trạng thái"
                rules={[{ required: true, message: 'Vui lòng chọn trạng thái!' }]}
              >
                <Select placeholder="Chọn trạng thái">
                  <Option value="dang_giao_dich">Đang giao dịch</Option>
                  <Option value="ngung_giao_dich">Ngừng giao dịch</Option>
                  <Option value="tam_khoa">Tạm khóa</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="vai_tro_ids"
                label="Vai trò"
              >
                <Select
                  mode="multiple"
                  placeholder="Chọn vai trò"
                  allowClear
                >
                  {rolesData?.data?.data?.roles?.map(role => (
                    <Option key={role.id} value={role.id}>
                      {role.ten_vai_tro}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createUserMutation.isLoading || updateUserMutation.isLoading}
              >
                {editingUser ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Users;
