import React, { useState } from 'react';
import { 
  Card, Descriptions, Button, Tag, Divider, Row, Col, 
  Statistic, Space, Spin, Typography, Modal, message 
} from 'antd';
import { 
  EditOutlined, DeleteOutlined, ArrowLeftOutlined,
  UserOutlined, PhoneOutlined, MailOutlined, HomeOutlined,
  TeamOutlined, ClockCircleOutlined, DollarOutlined,
  ShoppingOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { customerAPI } from '../../services/api';
import CustomerForm from './CustomerForm';

const { Title, Text } = Typography;
const { confirm } = Modal;

const CustomerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);

  // Fetch customer data
  const { data, isLoading, error } = useQuery(
    ['customer', id],
    () => customerAPI.getCustomer(id),
    {
      enabled: !!id,
      refetchOnWindowFocus: false
    }
  );

  const customer = data?.data?.customer;

  // Delete customer mutation
  const deleteCustomerMutation = useMutation(
    () => customerAPI.deleteCustomer(id),
    {
      onSuccess: () => {
        message.success('Xóa khách hàng thành công');
        navigate('/customers');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa khách hàng');
      }
    }
  );

  const showDeleteConfirm = () => {
    confirm({
      title: 'Bạn có chắc chắn muốn xóa khách hàng này?',
      icon: <ExclamationCircleOutlined />,
      content: 'Hành động này không thể hoàn tác.',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk() {
        deleteCustomerMutation.mutate();
      }
    });
  };

  const handleEditSuccess = () => {
    setIsEditModalVisible(false);
    queryClient.invalidateQueries(['customer', id]);
    message.success('Cập nhật khách hàng thành công');
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text type="danger">Có lỗi xảy ra khi tải thông tin khách hàng</Text>
        <br />
        <Button 
          type="primary" 
          onClick={() => navigate('/customers')}
          style={{ marginTop: '20px' }}
        >
          Quay lại danh sách
        </Button>
      </div>
    );
  }

  // Format địa chỉ đầy đủ
  const fullAddress = [
    customer?.dia_chi,
    customer?.phuong_xa,
    customer?.quan_huyen,
    customer?.tinh_thanh
  ].filter(Boolean).join(', ');

  return (
    <>
      <Card
        title={
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/customers')}
            />
            <span>Chi tiết khách hàng</span>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<EditOutlined />} 
              type="primary"
              onClick={() => setIsEditModalVisible(true)}
            >
              Chỉnh sửa
            </Button>
            <Button 
              icon={<DeleteOutlined />} 
              danger
              onClick={showDeleteConfirm}
              loading={deleteCustomerMutation.isLoading}
            >
              Xóa
            </Button>
          </Space>
        }
      >
        <Row gutter={[24, 24]}>
          <Col span={16}>
            <Card>
              <Title level={4}>
                <UserOutlined /> {customer?.ho_ten}
                <Tag 
                  color={customer?.trang_thai === 'active' ? 'green' : 'red'} 
                  style={{ marginLeft: 12 }}
                >
                  {customer?.trang_thai === 'active' ? 'Đang hoạt động' : 'Ngừng hoạt động'}
                </Tag>
              </Title>
              <Text type="secondary">Mã khách hàng: {customer?.ma_khach_hang}</Text>

              <Divider />

              <Descriptions column={1} labelStyle={{ fontWeight: 'bold' }}>
                <Descriptions.Item label={<><PhoneOutlined /> Số điện thoại</>}>
                  {customer?.so_dien_thoai || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><MailOutlined /> Email</>}>
                  {customer?.email || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><HomeOutlined /> Địa chỉ</>}>
                  {fullAddress || 'Chưa cập nhật'}
                </Descriptions.Item>
                <Descriptions.Item label={<><TeamOutlined /> Nhóm khách hàng</>}>
                  {customer?.nhomKhachHang?.ten_nhom || 'Chưa phân nhóm'}
                </Descriptions.Item>
                {customer?.ghi_chu && (
                  <Descriptions.Item label="Ghi chú">
                    {customer.ghi_chu}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          </Col>

          <Col span={8}>
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Tổng chi tiêu"
                    value={customer?.tong_chi_tieu || 0}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Số đơn hàng"
                    value={customer?.tong_sl_don_hang || 0}
                    prefix={<ShoppingOutlined />}
                  />
                </Card>
              </Col>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="Công nợ hiện tại"
                    value={customer?.cong_no_hien_tai || 0}
                    valueStyle={{ color: customer?.cong_no_hien_tai > 0 ? '#cf1322' : '#3f8600' }}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
            </Row>
          </Col>
        </Row>

        <Divider />

        <Descriptions column={2} labelStyle={{ fontWeight: 'bold' }}>
          <Descriptions.Item label={<><ClockCircleOutlined /> Ngày tạo</>}>
            {customer?.ngay_tao ? new Date(customer.ngay_tao).toLocaleString('vi-VN') : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Người tạo">
            {customer?.nguoi_tao || 'N/A'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Edit Modal */}
      <CustomerForm
        visible={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        onSuccess={handleEditSuccess}
        initialData={customer}
      />
    </>
  );
};

export default CustomerDetail;
