import React, { useState } from 'react';
import { 
  <PERSON>, Typo<PERSON>, Button, Input, Tabs, Space, 
  message, Row, Col, Tooltip
} from 'antd';
import { 
  PlusOutlined, SearchOutlined, DownloadOutlined, 
  UploadOutlined, FilterOutlined, ReloadOutlined 
} from '@ant-design/icons';
import { PageHeader, DataTable } from '../../components/Common';
import { customerAPI } from '../../services/api';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import CustomerForm from './CustomerForm';

const { TabPane } = Tabs;

const Customers = () => {
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const navigate = useNavigate();

  // Fetch customers
  const { data: customersData, isLoading, refetch } = useQuery(
    ['customers', currentPage, pageSize, searchText, activeTab],
    () => customerAPI.getCustomers({
      page: currentPage,
      limit: pageSize,
      search: searchText,
      trang_thai: activeTab !== 'all' ? activeTab : undefined
    }),
    {
      keepPreviousData: true
    }
  );

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleExport = async () => {
    try {
      const response = await customerAPI.exportCustomers();
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'customers.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
      message.success('Xuất file thành công');
    } catch (error) {
      message.error('Xuất file thất bại');
    }
  };

  const handleImport = () => {
    // TODO: Implement import functionality
    message.info('Chức năng nhập file đang được phát triển');
  };

  const handleAddCustomer = () => {
    setIsModalVisible(true);
  };

  const handleViewCustomer = (record) => {
    navigate(`/customers/${record.id}`);
  };

  const columns = [
    {
      title: 'Mã khách hàng',
      dataIndex: 'ma_khach_hang',
      key: 'ma_khach_hang',
      width: 150,
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'ho_ten',
      key: 'ho_ten',
      sorter: true,
      render: (text, record) => (
        <a onClick={() => handleViewCustomer(record)}>{text}</a>
      ),
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'so_dien_thoai',
      key: 'so_dien_thoai',
    },
    {
      title: 'Nhóm khách hàng',
      dataIndex: 'nhom_khach_hang',
      key: 'nhom_khach_hang',
    },
    {
      title: 'Công nợ hiện tại',
      dataIndex: 'cong_no_hien_tai',
      key: 'cong_no_hien_tai',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
      sorter: true,
    },
    {
      title: 'Tổng chi tiêu',
      dataIndex: 'tong_chi_tieu',
      key: 'tong_chi_tieu',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
      sorter: true,
    },
    {
      title: 'Tổng SL đơn hàng',
      dataIndex: 'tong_sl_don_hang',
      key: 'tong_sl_don_hang',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
    }
  ];

  return (
    <div>
      <PageHeader
        title="Khách hàng"
        actions={[
          {
            type: 'primary',
            icon: <PlusOutlined />,
            label: 'Thêm khách hàng',
            onClick: handleAddCustomer,
          },
        ]}
      />
      
      <Card style={{ marginBottom: 16 }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          tabBarExtraContent={
            <Space>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>Xuất file</Button>
              <Button icon={<UploadOutlined />} onClick={handleImport}>Nhập file</Button>
              <Button icon={<FilterOutlined />}>Bộ lọc</Button>
              <Button icon={<ReloadOutlined />} onClick={() => refetch()}>Làm mới</Button>
            </Space>
          }
        >
          <TabPane tab="Tất cả khách hàng" key="all" />
          <TabPane tab="Đang giao dịch" key="dang_giao_dich" />
        </Tabs>
        
        <Row style={{ marginTop: 16, marginBottom: 16 }}>
          <Col span={16}>
            <Input
              placeholder="Tìm kiếm theo mã khách hàng, tên, SĐT khách hàng"
              prefix={<SearchOutlined />}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '100%' }}
              allowClear
            />
          </Col>
        </Row>
        
        <DataTable
          columns={columns}
          dataSource={customersData?.data?.data?.customers || []}
          loading={isLoading}
          rowKey="id"
          showSearch={false}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customersData?.data?.data?.pagination?.total || 0,
            onChange: handlePageChange,
            showSizeChanger: true,
            showTotal: (total) => `Tổng ${total} khách hàng`
          }}
          rowSelection={{
            type: 'checkbox',
          }}
        />
      </Card>

      {isModalVisible && (
        <CustomerForm
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onSuccess={() => {
            setIsModalVisible(false);
            refetch();
            message.success('Thêm khách hàng thành công');
          }}
        />
      )}
    </div>
  );
};

export default Customers;
