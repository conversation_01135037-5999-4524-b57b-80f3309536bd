import React, { useState, useEffect } from 'react';
import { 
  Modal, Form, Input, Select, Button, message, 
  Spin, Switch, Divider, Row, Col, Space
} from 'antd';
import { 
  UserOutlined, PhoneOutlined, MailOutlined, 
  HomeOutlined, TeamOutlined, FileTextOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import { customerAPI } from '../../services/api';
import { useMutation, useQuery } from 'react-query';

const { Option } = Select;
const { TextArea } = Input;

const CustomerForm = ({ visible, onCancel, onSuccess, initialData }) => {
  const [form] = Form.useForm();
  const isEditing = !!initialData;

  // Fetch customer groups
  const { data: groupsData, isLoading: isLoadingGroups } = useQuery(
    'customerGroups',
    () => customerAPI.getAllCustomerGroup(),
    {
      enabled: visible
    }
  );

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        ...initialData,
        nhom_khach_hang_id: initialData.nhomKhachHang?.id,
        trang_thai: initialData.trang_thai === 'active'
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        trang_thai: true
      });
    }
  }, [initialData, form, visible]);

  // Create or update customer
  const mutation = useMutation(
    (values) => isEditing 
      ? customerAPI.updateCustomer(initialData.id, values)
      : customerAPI.createCustomer(values),
    {
      onSuccess: () => {
        onSuccess();
        form.resetFields();
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra');
      }
    }
  );

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        // Convert boolean to string for status
        if (values.trang_thai !== undefined) {
          values.trang_thai = values.trang_thai ? 'active' : 'inactive';
        }
        mutation.mutate(values);
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  return (
    <Modal
      title={isEditing ? 'Cập nhật khách hàng' : 'Thêm khách hàng mới'}
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={null}
      destroyOnClose
    >
      <Spin spinning={isLoadingGroups || mutation.isLoading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            trang_thai: true
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ho_ten"
                label="Tên khách hàng"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên khách hàng!' },
                  { min: 2, message: 'Tên khách hàng phải có ít nhất 2 ký tự!' },
                  { max: 100, message: 'Tên khách hàng không được quá 100 ký tự!' }
                ]}
              >
                <Input 
                  prefix={<UserOutlined />} 
                  placeholder="Nhập tên khách hàng" 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="so_dien_thoai"
                label="Số điện thoại"
                rules={[
                  { pattern: /^[0-9]{10,11}$/, message: 'Số điện thoại không hợp lệ!' }
                ]}
              >
                <Input 
                  prefix={<PhoneOutlined />} 
                  placeholder="Nhập số điện thoại" 
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { type: 'email', message: 'Email không hợp lệ!' }
                ]}
              >
                <Input 
                  prefix={<MailOutlined />} 
                  placeholder="Nhập email" 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nhom_khach_hang_id"
                label="Nhóm khách hàng"
              >
                <Select 
                  placeholder="Chọn nhóm khách hàng"
                  loading={isLoadingGroups}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {groupsData?.data?.data?.map(group => (
                    <Option key={group.id} value={group.id}>
                      {group.ten_nhom}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="dia_chi"
            label="Địa chỉ"
          >
            <Input 
              prefix={<HomeOutlined />} 
              placeholder="Nhập địa chỉ chi tiết" 
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="phuong_xa"
                label="Phường/Xã"
              >
                <Input 
                  placeholder="Nhập phường/xã" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="quan_huyen"
                label="Quận/Huyện"
              >
                <Input 
                  placeholder="Nhập quận/huyện" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="tinh_thanh"
                label="Tỉnh/Thành phố"
              >
                <Input 
                  placeholder="Nhập tỉnh/thành phố" 
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="ghi_chu"
            label="Ghi chú"
          >
            <TextArea 
              rows={3} 
              placeholder="Nhập ghi chú về khách hàng (tùy chọn)" 
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="trang_thai"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="Hoạt động"
              unCheckedChildren="Ngừng hoạt động"
            />
          </Form.Item>

          {isEditing && (
            <>
              <Divider />
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Người tạo">
                    <Input 
                      value={initialData?.nguoi_tao || 'N/A'} 
                      disabled 
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Ngày tạo">
                    <Input 
                      value={initialData?.ngay_tao ? new Date(initialData.ngay_tao).toLocaleString('vi-VN') : 'N/A'} 
                      disabled 
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}

          <Divider />
          
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel}>
                Hủy
              </Button>
              <Button 
                type="primary" 
                onClick={handleSubmit}
                loading={mutation.isLoading}
              >
                {isEditing ? 'Cập nhật' : 'Thêm mới'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default CustomerForm;

