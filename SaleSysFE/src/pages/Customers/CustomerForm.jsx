import React, { useState, useEffect } from 'react';
import { 
  Modal, Form, Input, Select, Button, message, 
  Spin, Divider, Row, Col, Space, DatePicker
} from 'antd';
import { 
  UserOutlined, PhoneOutlined, MailOutlined, 
  HomeOutlined, TeamOutlined, FileTextOutlined,
  EnvironmentOutlined, CalendarOutlined, IdcardOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { customerAPI } from '../../services/api';
import { employeeAPI } from '../../services/api/index';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

const trang_thai_nguoi_dung = [
  { value: 'dang_giao_dich', label: 'Đang giao dịch' },
  { value: 'ngung_giao_dich', label: 'Ngừng giao dịch' },
  { value: 'tam_khoa', label: 'Tạm khóa' }
];

const gioi_tinh_options = [
  { value: 'nam', label: 'Nam' },
  { value: 'nu', label: 'Nữ' },
  { value: 'khac', label: 'Khác' }
];

const CustomerForm = ({ visible, onCancel, onSuccess, initialData }) => {
  const [form] = Form.useForm();
  const isEditing = !!initialData;

  // Fetch customer groups
  const { data: groupsData, isLoading: isLoadingGroups } = useQuery(
    'customerGroups',
    () => customerAPI.getCustomerGroups(),
    {
      enabled: visible
    }
  );

  // Fetch employees
  const { data: employeesData, isLoading: isLoadingEmployees } = useQuery(
    'employees',
    () => employeeAPI.getEmployees({ loai_nguoi_dung: 'nhan_vien', trang_thai: 'active' }),
    {
      enabled: visible
    }
  );

  useEffect(() => {
    if (initialData) {
      console.log('Form initialData:', initialData);
      
      // Đảm bảo tất cả các trường được điền đúng
      form.setFieldsValue({
        ho_ten: initialData.ho_ten,
        so_dien_thoai: initialData.so_dien_thoai,
        email: initialData.email,
        dia_chi: initialData.dia_chi,
        phuong_xa: initialData.phuong_xa,
        quan_huyen: initialData.quan_huyen,
        tinh_thanh: initialData.tinh_thanh,
        nhom_khach_hang_id: initialData.nhomKhachHang?.id,
        nhan_vien_id: initialData.nhanVienPhuTrach?.id,
        trang_thai: initialData.trang_thai,
        ghi_chu: initialData.ghi_chu,
        
        // Các trường mới
        ngay_sinh: initialData.ngay_sinh ? dayjs(initialData.ngay_sinh) : null,
        gioi_tinh: initialData.gioi_tinh,
        ma_so_thue: initialData.ma_so_thue,
        website: initialData.website
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        trang_thai: 'dang_giao_dich'
      });
    }
  }, [initialData, form, visible]);

  // Create or update customer
  const mutation = useMutation(
    (values) => isEditing 
      ? customerAPI.updateCustomer(initialData.id, values)
      : customerAPI.createCustomer(values),
    {
      onSuccess: () => {
        onSuccess();
        form.resetFields();
        message.success(isEditing ? 'Cập nhật khách hàng thành công' : 'Thêm khách hàng thành công');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra');
      }
    }
  );

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        // Xử lý ngày sinh để đảm bảo định dạng đúng
        const formattedValues = {
          ...values,
          ngay_sinh: values.ngay_sinh ? values.ngay_sinh.format('YYYY-MM-DD') : null,
          // Đảm bảo website có định dạng đúng
          website: values.website ? (values.website.startsWith('http') ? values.website : `https://${values.website}`) : null
        };
        
        mutation.mutate(formattedValues);
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  return (
    <Modal
      title={isEditing ? 'Cập nhật khách hàng' : 'Thêm khách hàng mới'}
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={null}
      destroyOnClose
    >
      <Spin spinning={isLoadingGroups || isLoadingEmployees || mutation.isLoading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            trang_thai: 'dang_giao_dich'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ho_ten"
                label="Tên khách hàng"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên khách hàng!' },
                  { min: 2, message: 'Tên khách hàng phải có ít nhất 2 ký tự!' },
                  { max: 100, message: 'Tên khách hàng không được quá 100 ký tự!' }
                ]}
              >
                <Input 
                  prefix={<UserOutlined />} 
                  placeholder="Nhập tên khách hàng" 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="so_dien_thoai"
                label="Số điện thoại"
                rules={[
                  { pattern: /^[0-9]{10,11}$/, message: 'Số điện thoại không hợp lệ!' }
                ]}
              >
                <Input 
                  prefix={<PhoneOutlined />} 
                  placeholder="Nhập số điện thoại" 
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { type: 'email', message: 'Email không hợp lệ!' }
                ]}
              >
                <Input 
                  prefix={<MailOutlined />} 
                  placeholder="Nhập email" 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nhom_khach_hang_id"
                label="Nhóm khách hàng"
              >
                <Select 
                  placeholder="Chọn nhóm khách hàng"
                  loading={isLoadingGroups}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {groupsData?.data?.data?.map(group => (
                    <Option key={group.id} value={group.id}>
                      {group.ten_nhom}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="gioi_tinh"
                label="Giới tính"
              >
                <Select
                  placeholder="Chọn giới tính"
                  allowClear
                >
                  {gioi_tinh_options.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ngay_sinh"
                label="Ngày sinh"
              >
                <DatePicker 
                  style={{ width: '100%' }} 
                  format="DD/MM/YYYY"
                  placeholder="Chọn ngày sinh"
                  prefix={<CalendarOutlined />}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ma_so_thue"
                label="Mã số thuế"
                // Loại bỏ rules bắt buộc
              >
                <Input 
                  prefix={<IdcardOutlined />} 
                  placeholder="Nhập mã số thuế" 
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="website"
                label="Website"
                rules={[
                  { type: 'url', message: 'Website không hợp lệ!', warningOnly: true }
                ]}
                // Thêm warningOnly để chỉ cảnh báo mà không ngăn submit
              >
                <Input 
                  prefix={<GlobalOutlined />} 
                  placeholder="Nhập website" 
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="nhan_vien_id"
            label="Nhân viên phụ trách"
          >
            <Select
              placeholder="Chọn nhân viên phụ trách"
              loading={isLoadingEmployees}
              allowClear
              showSearch
              optionFilterProp="children"
            >
              {employeesData?.data?.data?.employees?.map(employee => (
                <Option key={employee.id} value={employee.id}>
                  {employee.ho_ten}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="dia_chi"
            label="Địa chỉ"
          >
            <Input 
              prefix={<HomeOutlined />} 
              placeholder="Nhập địa chỉ chi tiết" 
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="phuong_xa"
                label="Phường/Xã"
              >
                <Input 
                  placeholder="Nhập phường/xã" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="quan_huyen"
                label="Quận/Huyện"
              >
                <Input 
                  placeholder="Nhập quận/huyện" 
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="tinh_thanh"
                label="Tỉnh/Thành phố"
              >
                <Input 
                  placeholder="Nhập tỉnh/thành phố" 
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="ghi_chu"
            label="Ghi chú"
          >
            <TextArea 
              rows={3} 
              placeholder="Nhập ghi chú về khách hàng (tùy chọn)" 
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="trang_thai"
            label="Trạng thái"
            rules={[{ required: true, message: 'Vui lòng chọn trạng thái!' }]}
          >
            <Select 
              placeholder="Chọn trạng thái người dùng"
              allowClear
              showSearch
              optionFilterProp="children"
            >
              {trang_thai_nguoi_dung.map(status => (
                <Option key={status.value} value={status.value}>
                  {status.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {isEditing && (
            <>
              <Divider />
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Người tạo">
                    <Input 
                      value={initialData?.nguoi_tao || 'N/A'} 
                      disabled 
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Ngày tạo">
                    <Input 
                      value={initialData?.ngay_tao ? new Date(initialData.ngay_tao).toLocaleString('vi-VN') : 'N/A'} 
                      disabled 
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}

          <Divider />
          
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel}>
                Hủy
              </Button>
              <Button 
                type="primary" 
                onClick={handleSubmit}
                loading={mutation.isLoading}
              >
                {isEditing ? 'Cập nhật' : 'Thêm mới'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default CustomerForm;












