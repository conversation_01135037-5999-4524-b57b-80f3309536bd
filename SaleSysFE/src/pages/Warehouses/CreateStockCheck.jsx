import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Table,
  Image,
  InputNumber,
  Typography,
  Divider,
  message,
  Modal,
  Tag
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  SaveOutlined,
  CheckOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useWarehouses, useCreateStockCheck } from '../../hooks/useWarehouses';
import { useQuery } from 'react-query';
import { warehousesAPI } from '../../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateStockCheck = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isAddProductModalVisible, setIsAddProductModalVisible] = useState(false);
  const [isBalanceModalVisible, setIsBalanceModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [modalSearchText, setModalSearchText] = useState('');

  // API hooks
  const { data: warehouses = [], isLoading: warehousesLoading } = useWarehouses();
  const createStockCheckMutation = useCreateStockCheck();

  // Auto select default warehouse and set default values when warehouses are loaded
  useEffect(() => {
    if (warehouses.length > 0 && !form.getFieldValue('kho_hang_id')) {
      // Find "Kho chính" or use first warehouse
      const defaultWarehouse = warehouses.find(w =>
        w.ten_kho?.toLowerCase().includes('chính') ||
        w.ten_kho?.toLowerCase().includes('main')
      ) || warehouses[0];

      if (defaultWarehouse) {
        form.setFieldsValue({
          kho_hang_id: defaultWarehouse.id,
          ngay_kiem_ke: dayjs(), // Set current date as default
          ten_kiem_ke: `Kiểm kê ${dayjs().format('DD/MM/YYYY')}`
        });
        console.log('🏪 Auto-selected warehouse:', defaultWarehouse.ten_kho);
      }
    }
  }, [warehouses, form]);

  // Get available products for selected warehouse
  const selectedWarehouseId = Form.useWatch('kho_hang_id', form);
  const { data: availableProducts = [], isLoading: productsLoading } = useQuery(
    ['available-products', selectedWarehouseId],
    () => {
      console.log('🔍 Fetching products for warehouse:', selectedWarehouseId);
      return warehousesAPI.getInventory({ kho_hang_id: selectedWarehouseId });
    },
    {
      enabled: !!selectedWarehouseId,
      select: (response) => {
        console.log('📦 Raw inventory response:', response);
        const inventory = response.data?.data || [];
        console.log('📦 Inventory data:', inventory);
        const products = inventory.map(item => ({
          id: item.phien_ban_id,
          ten_san_pham: item.ten_san_pham,
          ten_phien_ban: item.phien_ban,
          ma_sku: item.ma_phien_ban,
          anh: item.anh_san_pham || 'https://via.placeholder.com/50x50',
          don_vi_tinh: item.don_vi_tinh || 'Cái',
          so_luong_ton: item.ton_kho || 0,
          so_luong_thuc_te: null,
          ly_do: '',
          ghi_chu: ''
        }));
        console.log('📦 Processed products:', products);
        return products;
      }
    }
  );

  // Handle create draft (Tạo phiếu)
  const handleCreateDraft = async () => {
    try {
      const values = await form.validateFields();
      if (selectedProducts.length === 0) {
        message.warning('Vui lòng thêm ít nhất một sản phẩm để kiểm kê');
        return;
      }

      const stockCheckData = {
        ten_kiem_ke: values.ten_kiem_ke,
        kho_hang_id: values.kho_hang_id,
        ngay_bat_dau: values.ngay_kiem_ke.format('YYYY-MM-DD'),
        ghi_chu: values.ghi_chu,
        products: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong_he_thong: product.so_luong_ton || 0,
          so_luong_thuc_te: product.so_luong_thuc_te || null,
          ly_do: product.ly_do || '',
          ghi_chu: product.ghi_chu || ''
        }))
      };

      await createStockCheckMutation.mutateAsync(stockCheckData);
      message.success('Tạo phiếu kiểm kê thành công! Chuyển sang trạng thái đang kiểm kho.');
      navigate('/warehouses/stock-check');
    } catch (error) {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
    }
  };

  // Handle balance confirmation
  const handleBalanceConfirm = () => {
    setIsBalanceModalVisible(true);
  };

  // Handle balance warehouse (Cân bằng kho)
  const handleBalanceWarehouse = async () => {
    try {
      const values = await form.validateFields();
      if (selectedProducts.length === 0) {
        message.warning('Vui lòng thêm ít nhất một sản phẩm để cân bằng');
        return;
      }

      // Check if all products have actual quantity
      const missingQuantity = selectedProducts.filter(p => p.so_luong_thuc_te === null || p.so_luong_thuc_te === undefined);
      if (missingQuantity.length > 0) {
        message.warning('Vui lòng nhập số lượng thực tế cho tất cả sản phẩm');
        return;
      }

      const balanceData = {
        ten_kiem_ke: values.ten_kiem_ke,
        kho_hang_id: values.kho_hang_id,
        nhan_vien_kiem: values.nhan_vien_kiem,
        ngay_kiem_ke: values.ngay_kiem_ke.format('YYYY-MM-DD'),
        ghi_chu: values.ghi_chu,
        trang_thai: 'hoan_thanh', // Completed status
        products: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong_he_thong: product.so_luong_ton || 0,
          so_luong_thuc_te: product.so_luong_thuc_te,
          ly_do: product.ly_do || '',
          ghi_chu: product.ghi_chu || ''
        }))
      };

      console.log('Balancing warehouse:', balanceData);
      setIsBalanceModalVisible(false);
      message.success('Cân bằng kho thành công!');
      navigate('/warehouses/stock-check');
    } catch (error) {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
    }
  };

  // Handle save draft
  const handleSaveDraft = async () => {
    try {
      const values = await form.validateFields(['ten_kiem_ke', 'kho_hang_id']);
      console.log('Saving draft:', values, selectedProducts);
      message.success('Lưu nháp thành công!');
    } catch (error) {
      message.error('Vui lòng điền đầy đủ thông tin cơ bản');
    }
  };

  // Handle add product
  const handleAddProduct = () => {
    setIsAddProductModalVisible(true);
  };

  // Handle remove product
  const handleRemoveProduct = (productId) => {
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
  };

  // Handle update product quantity
  const handleUpdateProduct = (productId, field, value) => {
    setSelectedProducts(prev => 
      prev.map(p => 
        p.id === productId ? { ...p, [field]: value } : p
      )
    );
  };

  // Filter available products based on modal search
  const filteredProducts = availableProducts.filter(product => {
    const searchLower = modalSearchText.toLowerCase();
    return (
      (product.ten_san_pham || '').toLowerCase().includes(searchLower) ||
      (product.ten_phien_ban || '').toLowerCase().includes(searchLower) ||
      (product.ma_sku || '').toLowerCase().includes(searchLower)
    );
  });

  // Table columns
  const columns = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Ảnh',
      dataIndex: 'anh',
      key: 'anh',
      width: 80,
      align: 'center',
      render: (anh, record) => (
        <Image
          src={anh}
          alt={record.ten_san_pham}
          width={50}
          height={50}
          style={{ objectFit: 'cover', borderRadius: 4 }}
        />
      )
    },
    {
      title: 'Tên sản phẩm',
      key: 'product',
      width: 250,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.ten_phien_ban} - {record.ma_sku}
          </Text>
        </div>
      )
    },
    {
      title: 'Đơn vị',
      dataIndex: 'don_vi_tinh',
      key: 'don_vi_tinh',
      width: 80,
      align: 'center'
    },
    {
      title: 'Tồn thực tế',
      key: 'so_luong_thuc_te',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <InputNumber
          value={record.so_luong_thuc_te}
          onChange={(value) => handleUpdateProduct(record.id, 'so_luong_thuc_te', value)}
          placeholder="Nhập SL"
          min={0}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Lý do',
      key: 'ly_do',
      width: 150,
      render: (_, record) => (
        <Select
          value={record.ly_do}
          onChange={(value) => handleUpdateProduct(record.id, 'ly_do', value)}
          placeholder="Chọn lý do"
          style={{ width: '100%' }}
          size="small"
          allowClear
        >
          <Option value="">Không có</Option>
          <Option value="hang_hong">Hàng hỏng</Option>
          <Option value="mat_hang">Mất hàng</Option>
          <Option value="sai_so">Sai số</Option>
          <Option value="khac">Khác</Option>
        </Select>
      )
    },
    {
      title: 'Ghi chú',
      key: 'ghi_chu',
      width: 200,
      render: (_, record) => (
        <Input
          value={record.ghi_chu}
          onChange={(e) => handleUpdateProduct(record.id, 'ghi_chu', e.target.value)}
          placeholder="Nhập ghi chú"
          size="small"
        />
      )
    },
    {
      title: '',
      key: 'actions',
      width: 50,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveProduct(record.id)}
          danger
          size="small"
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        padding: '16px 24px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/warehouses/stock-check')}
          >
            Tạo phiếu kiểm hàng
          </Button>
        </Space>
        <Space>
          <Button onClick={handleCreateDraft}>
            Tạo phiếu
          </Button>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleBalanceConfirm}
          >
            Cân bằng kho
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
      >
        {/* Form thông tin phiếu */}
        <Row gutter={24} style={{ marginBottom: 24 }}>
          {/* Cột trái - Thông tin phiếu */}
          <Col span={12}>
            <Card title="Thông tin phiếu" style={{ height: '100%' }}>
              <Form.Item
                name="ten_kiem_ke"
                label="Tên phiếu kiểm"
                rules={[{ required: true, message: 'Vui lòng nhập tên phiếu kiểm' }]}
              >
                <Input placeholder="Nhập tên phiếu kiểm" />
              </Form.Item>

              <Form.Item
                name="kho_hang_id"
                label="Kho"
                rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
              >
                <Select placeholder="Chọn kho">
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.ten_kho}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="ma_phieu"
                label="Mã phiếu"
              >
                <Input placeholder="Tự động tạo" disabled />
              </Form.Item>
            </Card>
          </Col>

          {/* Cột phải - Thông tin bổ sung */}
          <Col span={12}>
            <Card title="Thông tin bổ sung" style={{ height: '100%' }}>
              <Form.Item
                name="nhan_vien_kiem"
                label="Nhân viên kiểm"
                rules={[{ required: true, message: 'Vui lòng nhập tên nhân viên' }]}
              >
                <Input placeholder="Nhập tên nhân viên kiểm" />
              </Form.Item>

              <Form.Item
                name="ngay_kiem_ke"
                label="Ngày"
                rules={[{ required: true, message: 'Vui lòng chọn ngày' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>

              <Form.Item
                name="ghi_chu"
                label="Ghi chú"
              >
                <TextArea rows={3} placeholder="Nhập ghi chú" />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* Bảng sản phẩm */}
        <Card 
          title="Thông tin sản phẩm"
          extra={
            <Space>
              <Input
                placeholder="Tìm kiếm sản phẩm..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 200 }}
              />
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddProduct}
              >
                Thêm sản phẩm vào phiếu
              </Button>
            </Space>
          }
        >
          <Table
            columns={columns}
            dataSource={selectedProducts}
            rowKey="id"
            pagination={false}
            locale={{
              emptyText: (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Text type="secondary">
                    Chưa có sản phẩm nào được chọn để kiểm kê
                  </Text>
                  <br />
                  <Button
                    type="link"
                    icon={<PlusOutlined />}
                    onClick={handleAddProduct}
                  >
                    Thêm sản phẩm
                  </Button>
                </div>
              )
            }}
          />

          {/* Thông tin tổng kết */}
          {selectedProducts.length > 0 && (
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Space>
                <Text>Hiển thị:</Text>
                <Text strong>{selectedProducts.length}</Text>
                <Text>kết quả</Text>
                <Text>Tổ 1 đến 1 trên tổng 1</Text>
              </Space>
              <Button type="link" style={{ padding: 0 }}>
                ⚙️
              </Button>
            </div>
          )}
        </Card>
      </Form>

      {/* Modal thêm sản phẩm */}
      <Modal
        title="Thêm sản phẩm vào phiếu kiểm kê"
        open={isAddProductModalVisible}
        onCancel={() => setIsAddProductModalVisible(false)}
        footer={null}
        width={800}
      >
        {!selectedWarehouseId ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">
              Vui lòng chọn kho hàng trước khi thêm sản phẩm
            </Text>
          </div>
        ) : (
          <div>
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              prefix={<SearchOutlined />}
              value={modalSearchText}
              onChange={(e) => setModalSearchText(e.target.value)}
              style={{ width: '100%', marginBottom: 16 }}
              allowClear
            />
            <Table
              columns={[
                {
                  title: 'Sản phẩm',
                  key: 'product',
                  render: (_, record) => (
                    <div>
                      <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
                      <Text type="secondary">{record.ten_phien_ban}</Text>
                    </div>
                  )
                },
                {
                  title: 'Tồn kho',
                  dataIndex: 'so_luong_ton',
                  key: 'so_luong_ton',
                  align: 'center'
                },
                {
                  title: 'Thao tác',
                  key: 'action',
                  align: 'center',
                  render: (_, record) => (
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        if (!selectedProducts.find(p => p.id === record.id)) {
                          setSelectedProducts(prev => [...prev, record]);
                        }
                        setIsAddProductModalVisible(false);
                      }}
                      disabled={selectedProducts.find(p => p.id === record.id)}
                    >
                      {selectedProducts.find(p => p.id === record.id) ? 'Đã thêm' : 'Thêm'}
                    </Button>
                  )
                }
              ]}
              dataSource={filteredProducts}
              loading={productsLoading}
              rowKey="id"
              pagination={false}
            />
          </div>
        )}
      </Modal>

      {/* Modal xác nhận cân bằng */}
      <Modal
        title="Xác nhận cân bằng"
        open={isBalanceModalVisible}
        onCancel={() => setIsBalanceModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsBalanceModalVisible(false)}>
            Thoát
          </Button>,
          <Button key="confirm" type="primary" onClick={handleBalanceWarehouse}>
            Xác nhận
          </Button>
        ]}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <Text strong style={{ fontSize: '16px' }}>
            Tổng số mặt hàng cân bằng: {selectedProducts.length} sản phẩm
          </Text>
          <br /><br />
          <Text>
            Cân bằng nhằm cố định số lượng tồn kho thực tế của những sản phẩm trong danh sách
            kiểm theo đúng số lượng tồn thực tế hiện.
          </Text>
          <br /><br />
          <Text strong>Bạn có chắc chắn chưa?</Text>
        </div>
      </Modal>
    </div>
  );
};

export default CreateStockCheck;
