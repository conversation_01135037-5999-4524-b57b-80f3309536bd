import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  InputNumber,
  Form,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Alert,
  Modal,
  message,
  Popconfirm,
  Breadcrumb
} from 'antd';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import {
  useStockCheckDetails,
  useUpdateStockCheckItem,
  useCompleteStockCheck,
  useRemoveProductFromStockCheck
} from '../../hooks/useWarehouses';
import AddProductsModal from '../../components/AddProductsModal';

const { Title, Text } = Typography;
const { TextArea } = Input;

const StockCheckDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [editingItem, setEditingItem] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [showAddProductsModal, setShowAddProductsModal] = useState(false);

  // API hooks
  const { 
    data: stockCheckData, 
    isLoading, 
    refetch 
  } = useStockCheckDetails(id, {
    page: currentPage,
    limit: pageSize,
    search: searchText
  });

  const updateItemMutation = useUpdateStockCheckItem();
  const completeStockCheckMutation = useCompleteStockCheck();
  const removeProductMutation = useRemoveProductFromStockCheck();

  const stockCheck = stockCheckData?.stockCheck || {};
  const details = stockCheckData?.details || [];
  const pagination = stockCheckData?.pagination || {};

  // Handle edit item
  const handleEditItem = (item) => {
    setEditingItem(item.id);
    form.setFieldsValue({
      so_luong_thuc_te: item.so_luong_thuc_te,
      ghi_chu: item.ghi_chu
    });
  };

  // Handle save item
  const handleSaveItem = async (item) => {
    try {
      const values = await form.validateFields();
      await updateItemMutation.mutateAsync({
        stockCheckId: id,
        itemId: item.id,
        ...values
      });
      setEditingItem(null);
      form.resetFields();
      refetch();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingItem(null);
    form.resetFields();
  };

  // Handle complete stock check
  const handleCompleteStockCheck = async () => {
    try {
      await completeStockCheckMutation.mutateAsync(id);
      refetch();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Handle remove product
  const handleRemoveProduct = async (itemId) => {
    try {
      await removeProductMutation.mutateAsync({
        stockCheckId: id,
        itemId
      });
      refetch();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Handle add products success
  const handleAddProductsSuccess = () => {
    refetch();
  };

  // Get status tag
  const getStatusTag = (status) => {
    const statusMap = {
      chua_kiem: { color: 'default', text: 'Chưa kiểm' },
      da_kiem: { color: 'green', text: 'Đã kiểm' },
      co_lech: { color: 'red', text: 'Có lệch' }
    };
    const config = statusMap[status] || statusMap.chua_kiem;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // Get stock check status tag
  const getStockCheckStatusTag = (status) => {
    const statusMap = {
      ke_hoach: { color: 'blue', text: 'Kế hoạch' },
      dang_thuc_hien: { color: 'orange', text: 'Đang thực hiện' },
      hoan_thanh: { color: 'green', text: 'Hoàn thành' },
      huy: { color: 'red', text: 'Đã hủy' }
    };
    const config = statusMap[status] || statusMap.ke_hoach;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // Table columns
  const columns = [
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 250,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.san_pham_ten}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.phien_ban_ten} - {record.ma_sku}
          </Text>
        </div>
      )
    },
    {
      title: 'SL Hệ thống',
      dataIndex: 'so_luong_he_thong',
      key: 'so_luong_he_thong',
      width: 100,
      align: 'center',
      render: (value) => (
        <Text style={{ fontWeight: 500 }}>{value?.toLocaleString('vi-VN')}</Text>
      )
    },
    {
      title: 'SL Thực tế',
      key: 'so_luong_thuc_te',
      width: 120,
      align: 'center',
      render: (_, record) => {
        if (editingItem === record.id) {
          return (
            <Form.Item
              name="so_luong_thuc_te"
              style={{ margin: 0 }}
              rules={[
                { required: true, message: 'Vui lòng nhập số lượng' },
                { type: 'number', min: 0, message: 'Số lượng không thể âm' }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="Nhập SL"
                min={0}
              />
            </Form.Item>
          );
        }
        return record.so_luong_thuc_te !== null ? (
          <Text style={{ fontWeight: 500 }}>{record.so_luong_thuc_te?.toLocaleString('vi-VN')}</Text>
        ) : (
          <Text type="secondary">-</Text>
        );
      }
    },
    {
      title: 'Chênh lệch',
      key: 'so_luong_lech',
      width: 100,
      align: 'center',
      render: (_, record) => {
        if (record.so_luong_thuc_te === null) return <Text type="secondary">-</Text>;
        const isPositive = record.so_luong_lech > 0;
        const isNegative = record.so_luong_lech < 0;
        return (
          <Text 
            style={{ 
              fontWeight: 500,
              color: isPositive ? '#52c41a' : isNegative ? '#ff4d4f' : '#666'
            }}
          >
            {isPositive ? '+' : ''}{record.so_luong_lech?.toLocaleString('vi-VN')}
          </Text>
        );
      }
    },
    {
      title: 'Giá trị lệch',
      key: 'gia_tri_lech',
      width: 120,
      align: 'right',
      render: (_, record) => {
        if (record.so_luong_thuc_te === null) return <Text type="secondary">-</Text>;
        const isPositive = record.gia_tri_lech > 0;
        const isNegative = record.gia_tri_lech < 0;
        return (
          <Text 
            style={{ 
              fontWeight: 500,
              color: isPositive ? '#52c41a' : isNegative ? '#ff4d4f' : '#666'
            }}
          >
            {isPositive ? '+' : ''}{record.gia_tri_lech?.toLocaleString('vi-VN')}₫
          </Text>
        );
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 100,
      align: 'center',
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Ghi chú',
      key: 'ghi_chu',
      width: 200,
      render: (_, record) => {
        if (editingItem === record.id) {
          return (
            <Form.Item name="ghi_chu" style={{ margin: 0 }}>
              <TextArea
                rows={2}
                placeholder="Nhập ghi chú"
                style={{ fontSize: '12px' }}
              />
            </Form.Item>
          );
        }
        return record.ghi_chu ? (
          <Text style={{ fontSize: '12px' }}>{record.ghi_chu}</Text>
        ) : (
          <Text type="secondary" style={{ fontSize: '12px' }}>-</Text>
        );
      }
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      align: 'center',
      render: (_, record) => {
        if (stockCheck.trang_thai === 'hoan_thanh') {
          return <Text type="secondary">Đã hoàn thành</Text>;
        }

        if (editingItem === record.id) {
          return (
            <Space>
              <Button
                type="primary"
                size="small"
                icon={<SaveOutlined />}
                onClick={() => handleSaveItem(record)}
                loading={updateItemMutation.isLoading}
              >
                Lưu
              </Button>
              <Button
                size="small"
                icon={<CloseOutlined />}
                onClick={handleCancelEdit}
              >
                Hủy
              </Button>
            </Space>
          );
        }

        return (
          <Space>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditItem(record)}
            >
              Kiểm kê
            </Button>
            {record.so_luong_thuc_te === null && (
              <Popconfirm
                title="Xóa sản phẩm"
                description="Bạn có chắc chắn muốn xóa sản phẩm này khỏi phiếu kiểm kê?"
                onConfirm={() => handleRemoveProduct(record.id)}
                okText="Xóa"
                cancelText="Hủy"
              >
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                  loading={removeProductMutation.isLoading}
                >
                  Xóa
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      }
    }
  ];

  // Calculate progress
  const progress = stockCheck.tong_san_pham > 0 
    ? Math.round((stockCheck.san_pham_da_kiem / stockCheck.tong_san_pham) * 100)
    : 0;

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <Button 
            type="link" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/warehouses/stock-check')}
            style={{ padding: 0 }}
          >
            Kiểm kê kho hàng
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{stockCheck.ma_kiem_ke}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              {stockCheck.ten_kiem_ke}
            </Title>
            <Text type="secondary">Mã: {stockCheck.ma_kiem_ke}</Text>
          </Col>
          <Col>
            {getStockCheckStatusTag(stockCheck.trang_thai)}
          </Col>
          <Col>
            {stockCheck.trang_thai === 'dang_thuc_hien' && progress === 100 && (
              <Popconfirm
                title="Hoàn thành kiểm kê"
                description="Bạn có chắc chắn muốn hoàn thành kiểm kê? Hành động này không thể hoàn tác."
                onConfirm={handleCompleteStockCheck}
                okText="Hoàn thành"
                cancelText="Hủy"
                icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
              >
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  loading={completeStockCheckMutation.isLoading}
                >
                  Hoàn thành kiểm kê
                </Button>
              </Popconfirm>
            )}
          </Col>
        </Row>
      </Card>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng sản phẩm"
              value={stockCheck.tong_san_pham || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đã kiểm kê"
              value={stockCheck.san_pham_da_kiem || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Sản phẩm lệch"
              value={stockCheck.san_pham_lech || 0}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Giá trị lệch"
              value={stockCheck.gia_tri_lech || 0}
              prefix="₫"
              valueStyle={{ 
                color: (stockCheck.gia_tri_lech || 0) >= 0 ? '#ff4d4f' : '#52c41a' 
              }}
              formatter={(value) => value?.toLocaleString('vi-VN')}
            />
          </Card>
        </Col>
      </Row>

      {/* Progress */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Text strong>Tiến độ kiểm kê:</Text>
            <Progress 
              percent={progress} 
              status={progress === 100 ? 'success' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Col>
          <Col>
            <Text style={{ fontSize: '16px', fontWeight: 500 }}>
              {stockCheck.san_pham_da_kiem || 0}/{stockCheck.tong_san_pham || 0}
            </Text>
          </Col>
        </Row>
      </Card>

      {/* Alert for completion */}
      {stockCheck.trang_thai === 'dang_thuc_hien' && progress === 100 && (
        <Alert
          message="Đã kiểm kê xong tất cả sản phẩm"
          description="Bạn có thể hoàn thành kiểm kê để cập nhật tồn kho theo số liệu thực tế."
          type="success"
          showIcon
          action={
            <Popconfirm
              title="Hoàn thành kiểm kê"
              description="Bạn có chắc chắn muốn hoàn thành kiểm kê?"
              onConfirm={handleCompleteStockCheck}
              okText="Hoàn thành"
              cancelText="Hủy"
            >
              <Button size="small" type="primary">
                Hoàn thành ngay
              </Button>
            </Popconfirm>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Main Table */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Title level={5} style={{ margin: 0 }}>
                Chi tiết kiểm kê
              </Title>
            </Col>
            <Col>
              {stockCheck.trang_thai !== 'hoan_thanh' && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowAddProductsModal(true)}
                >
                  Thêm sản phẩm
                </Button>
              )}
            </Col>
            <Col flex="auto">
              <Input
                placeholder="Tìm kiếm sản phẩm..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 300, float: 'right' }}
                allowClear
              />
            </Col>
          </Row>
        </div>

        <Form form={form}>
          <Table
            columns={columns}
            dataSource={details}
            loading={isLoading}
            rowKey="id"
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: pagination.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} sản phẩm`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size);
              },
            }}
            scroll={{ x: 1200 }}
          />
        </Form>
      </Card>

      {/* Add Products Modal */}
      <AddProductsModal
        visible={showAddProductsModal}
        onCancel={() => setShowAddProductsModal(false)}
        stockCheckId={id}
        onSuccess={handleAddProductsSuccess}
      />
    </div>
  );
};

export default StockCheckDetails;
