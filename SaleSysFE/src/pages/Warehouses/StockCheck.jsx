import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Row,
  Col,
  Input,
  Select,
  Alert
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useStockChecks } from '../../hooks/useWarehouses';

const { Title, Text } = Typography;
const { Option } = Select;

const StockCheck = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // API hooks
  const { data: stockChecksData, isLoading: stockChecksLoading, error } = useStockChecks({
    search: searchText,
    trang_thai: selectedStatus
  });
  
  // stockChecksData is already the array from data.data (due to select in hook)
  const stockChecks = Array.isArray(stockChecksData) ? stockChecksData : [];
  console.log("Check data",stockChecksData)
  console.log('📋 Stock checks data:', {
    stockChecksData,
    stockChecks,
    isLoading: stockChecksLoading,
    error,
    stockChecksLength: stockChecks.length
  });

  // Show error message if there's an error
  if (error) {
    console.error('❌ Stock checks error:', error);
    // Show error notification for authentication issues
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.error('🚫 Authentication/Permission error:', error.response?.data?.message);
    }
  }

  const handleStatusFilter = (value) => {
    setSelectedStatus(value);
  };

  const getStatusTag = (status) => {
    const statusMap = {
      ke_hoach: { color: 'blue', text: 'Kế hoạch' },
      dang_thuc_hien: { color: 'green', text: 'Đang kiểm kho' },
      hoan_thanh: { color: 'cyan', text: 'Đã cân bằng' },
      huy: { color: 'red', text: 'Đã hủy' }
    };
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  const columns = [
    {
      title: 'Mã phiếu kiểm',
      dataIndex: 'ma_kiem_ke',
      key: 'ma_kiem_ke',
      width: 150,
      render: (text, record) => (
        <Text 
          strong 
          style={{ color: '#1890ff', cursor: 'pointer' }}
          onClick={() => navigate(`/warehouses/check/${record.id}`)}
        >
          {text}
        </Text>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 120,
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'ngay_bat_dau',
      key: 'ngay_bat_dau',
      width: 150,
      render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm')
    },
    {
      title: 'Ngày cân bằng',
      dataIndex: 'ngay_ket_thuc',
      key: 'ngay_ket_thuc',
      width: 150,
      render: (date) => date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-'
    },
    {
      title: 'Nhân viên tạo',
      dataIndex: 'nguoi_kiem_ke',
      key: 'nguoi_kiem_ke',
      width: 150
    },
    {
      title: 'Nhân viên kiểm',
      dataIndex: 'nguoi_kiem_ke',
      key: 'nguoi_kiem_ke_2',
      width: 150
    },
    {
      title: 'Nhân viên cân bằng',
      dataIndex: 'nguoi_kiem_ke',
      key: 'nguoi_kiem_ke_3',
      width: 150
    },
    {
      title: 'Ghi chú',
      dataIndex: 'ghi_chu',
      key: 'ghi_chu',
      width: 150,
      render: (text) => text || '-'
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Title level={3} style={{ margin: 0, color: '#262626' }}>
          Kiểm hàng
        </Title>
        <Space>
          <Button icon="❓" style={{ border: 'none', background: 'none' }}>
            Trợ giúp
          </Button>
          <Button icon="👤" style={{ border: 'none', background: 'none' }}>
            Góp ý
          </Button>
          <Button 
            type="primary" 
            style={{ backgroundColor: '#1890ff' }}
            onClick={() => navigate('/warehouses/check/create')}
          >
            Tạo phiếu kiểm
          </Button>
        </Space>
      </div>

      {/* Tabs */}
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button type="text" icon="📤">
            Xuất file
          </Button>
          <Button type="text" icon="📥">
            Nhập file
          </Button>
        </Space>
      </div>

      {/* Sub header */}
      <div style={{ marginBottom: '16px' }}>
        <Text style={{ color: '#1890ff', cursor: 'pointer' }}>
          Tất cả phiếu kiểm hàng
        </Text>
      </div>

      {/* Search and filters */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Input
              placeholder="Tìm kiếm theo mã phiếu kiểm hàng"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
              allowClear
            />
          </Col>
          <Col>
            <Select
              value={selectedStatus}
              onChange={handleStatusFilter}
              style={{ width: 120 }}
              placeholder="Trạng thái"
            >
              <Option value="">Tất cả</Option>
              <Option value="ke_hoach">Kế hoạch</Option>
              <Option value="dang_thuc_hien">Đang kiểm kho</Option>
              <Option value="hoan_thanh">Đã cân bằng</Option>
              <Option value="huy">Đã hủy</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="Ngày tạo"
              style={{ width: 120 }}
            >
              <Option value="">Tất cả</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="Chi nhánh"
              style={{ width: 120 }}
            >
              <Option value="">Tất cả</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="Bộ lọc khác"
              style={{ width: 120 }}
            >
              <Option value="">Tất cả</Option>
            </Select>
          </Col>
          <Col>
            <Button type="text">
              Lưu bộ lọc
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Show error alert if there's an authentication/permission error */}
      {error && (error.response?.status === 401 || error.response?.status === 403) && (
        <Alert
          message="Lỗi quyền truy cập"
          description={`Bạn không có quyền truy cập tính năng này. ${error.response?.data?.message || ''}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Show general error alert for other errors */}
      {error && error.response?.status !== 401 && error.response?.status !== 403 && (
        <Alert
          message="Lỗi tải dữ liệu"
          description={error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tải dữ liệu'}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Main table */}
      <Card style={{ padding: 0 }}>
        <div style={{ padding: '16px 24px', borderBottom: '1px solid #f0f0f0' }}>
          <Row align="middle">
            <Col>
              <input type="checkbox" style={{ marginRight: '8px' }} />
            </Col>
            <Col flex="auto">
              <Text strong>Mã phiếu kiểm</Text>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={Array.isArray(stockChecks) ? stockChecks : []}
          loading={stockChecksLoading}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1200 }}
          size="middle"
          style={{
            backgroundColor: 'white',
            borderRadius: '8px'
          }}
          rowSelection={{
            type: 'checkbox',
            onChange: (selectedRowKeys, selectedRows) => {
              console.log('Selected rows:', selectedRows);
            },
          }}
          locale={{
            emptyText: error
              ? `Lỗi tải dữ liệu: ${error.message || 'Unknown error'}`
              : 'Không có dữ liệu'
          }}
        />
      </Card>
    </div>
  );
};

export default StockCheck;
