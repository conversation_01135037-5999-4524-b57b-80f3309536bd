import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Steps,
  Progress,
  Alert,
  DatePicker,
  Popconfirm
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined,
  WarningOutlined,
  FileTextOutlined,
  ScanOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import {
  useStockChecks,
  useCreateStockCheck,
  useUpdateStockCheck,
  useDeleteStockCheck,
  useWarehouses
} from '../../hooks/useWarehouses';

const { Title, Text } = Typography;
const { Option } = Select;
const { Step } = Steps;

const StockCheck = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create', 'edit', 'view'
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // API hooks
  const { data: warehouses = [], isLoading: warehousesLoading } = useWarehouses();
  const {
    data: stockChecksResponse = { data: [], pagination: {} },
    isLoading: stockChecksLoading,
    refetch: refetchStockChecks
  } = useStockChecks({
    page: currentPage,
    limit: pageSize,
    search: searchText,
    kho_id: selectedWarehouse,
    trang_thai: selectedStatus
  });

  const createStockCheckMutation = useCreateStockCheck();
  const updateStockCheckMutation = useUpdateStockCheck();
  const deleteStockCheckMutation = useDeleteStockCheck();

  // Demo data for testing
  const demoStockChecks = [
    {
      id: 1,
      ma_kiem_ke: 'test001',
      ten_kiem_ke: 'Kiểm kê test001',
      ten_kho: 'Kho chính',
      trang_thai: 'dang_thuc_hien',
      ngay_bat_dau: '2025-06-23T15:38:00',
      ngay_ket_thuc: null,
      nguoi_kiem_ke: 'Dương',
      san_pham_da_kiem: 0,
      tong_san_pham: 1,
      san_pham_lech: 0,
      gia_tri_lech: 0,
      ghi_chu: 'test'
    }
  ];

  const stockChecks = stockChecksResponse.data?.length > 0 ? stockChecksResponse.data : demoStockChecks;
  const pagination = stockChecksResponse.pagination || {};

  // Handle search and filters
  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleWarehouseFilter = (value) => {
    setSelectedWarehouse(value);
    setCurrentPage(1);
  };

  const handleStatusFilter = (value) => {
    setSelectedStatus(value);
    setCurrentPage(1);
  };

  // Modal handlers
  const showModal = (type, record = null) => {
    setModalType(type);
    setSelectedRecord(record);
    setIsModalVisible(true);

    if (type === 'edit' && record) {
      form.setFieldsValue({
        ten_kiem_ke: record.ten_kiem_ke,
        kho_hang_id: record.kho_id,
        ngay_bat_dau: record.ngay_bat_dau ? dayjs(record.ngay_bat_dau) : null,
        ngay_ket_thuc: record.ngay_ket_thuc ? dayjs(record.ngay_ket_thuc) : null,
        ghi_chu: record.ghi_chu
      });
    } else {
      form.resetFields();
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedRecord(null);
    form.resetFields();
  };

  const handleSubmit = async (values) => {
    try {
      const submitData = {
        ten_kiem_ke: values.ten_kiem_ke,
        kho_hang_id: values.kho_hang_id,
        ngay_bat_dau: values.ngay_bat_dau.format('YYYY-MM-DD HH:mm:ss'),
        ngay_ket_thuc: values.ngay_ket_thuc ? values.ngay_ket_thuc.format('YYYY-MM-DD HH:mm:ss') : null,
        ghi_chu: values.ghi_chu || ''
      };

      if (modalType === 'create') {
        await createStockCheckMutation.mutateAsync(submitData);
      } else if (modalType === 'edit') {
        await updateStockCheckMutation.mutateAsync({
          id: selectedRecord.id,
          ...submitData
        });
      }

      setIsModalVisible(false);
      form.resetFields();
      refetchStockChecks();
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteStockCheckMutation.mutateAsync(id);
      refetchStockChecks();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleViewDetail = (check) => {
    showModal('view', check);
  };

  const getStatusTag = (status) => {
    const statusMap = {
      ke_hoach: { color: 'blue', text: 'Kế hoạch' },
      dang_thuc_hien: { color: 'green', text: 'Đang kiểm kho' },
      hoan_thanh: { color: 'cyan', text: 'Đã cân bằng' },
      huy: { color: 'red', text: 'Đã hủy' }
    };
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  const getProgressPercent = (check) => {
    if (check.trang_thai === 'hoan_thanh') return 100;
    if (check.trang_thai === 'ke_hoach') return 0;
    if (check.san_pham_da_kiem && check.tong_san_pham) {
      return Math.round((check.san_pham_da_kiem / check.tong_san_pham) * 100);
    }
    return 0;
  };

  const columns = [
    {
      title: 'Mã kiểm kê',
      dataIndex: 'ma_kiem_ke',
      key: 'ma_kiem_ke',
      width: 120,
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Tag color={getProgressPercent(record) === 100 ? 'green' : 'blue'} size="small">
            {getProgressPercent(record)}%
          </Tag>
        </div>
      )
    },
    {
      title: 'Tên kiểm kê',
      dataIndex: 'ten_kiem_ke',
      key: 'ten_kiem_ke',
      ellipsis: true
    },
    {
      title: 'Kho hàng',
      dataIndex: 'ten_kho',
      key: 'ten_kho',
      width: 150
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 120,
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Tiến độ',
      key: 'progress',
      width: 150,
      render: (_, record) => {
        const percent = getProgressPercent(record);
        return (
          <div>
            <Progress 
              percent={percent} 
              size="small" 
              status={percent === 100 ? 'success' : 'active'}
            />
            {record.san_pham_da_kiem && record.tong_san_pham && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.san_pham_da_kiem}/{record.tong_san_pham} sản phẩm
              </Text>
            )}
          </div>
        );
      }
    },
    {
      title: 'Chênh lệch',
      key: 'difference',
      width: 120,
      align: 'center',
      render: (_, record) => {
        if (record.trang_thai === 'ke_hoach') return '-';
        return (
          <div>
            <Text strong style={{ color: record.san_pham_lech > 0 ? '#ff4d4f' : '#52c41a' }}>
              {record.san_pham_lech || 0} SP
            </Text>
            {record.gia_tri_lech && (
              <>
                <br />
                <Text 
                  style={{ 
                    fontSize: '12px',
                    color: record.gia_tri_lech > 0 ? '#ff4d4f' : '#52c41a' 
                  }}
                >
                  {record.gia_tri_lech > 0 ? '+' : ''}{record.gia_tri_lech?.toLocaleString('vi-VN')} đ
                </Text>
              </>
            )}
          </div>
        );
      }
    },
    {
      title: 'Ngày thực hiện',
      key: 'dates',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{dayjs(record.ngay_bat_dau).format('DD/MM/YYYY')}</div>
          {record.ngay_ket_thuc && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              đến {dayjs(record.ngay_ket_thuc).format('DD/MM/YYYY')}
            </Text>
          )}
        </div>
      )
    },
    {
      title: 'Người kiểm kê',
      dataIndex: 'nguoi_kiem_ke',
      key: 'nguoi_kiem_ke',
      width: 120
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <Space>
          {(record.trang_thai === 'ke_hoach' || record.trang_thai === 'dang_thuc_hien') && (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => navigate(`/warehouses/check/${record.id}`)}
              size="small"
            >
              {record.trang_thai === 'ke_hoach' ? 'Bắt đầu' : 'Tiếp tục'}
            </Button>
          )}
          <Button
            type="text"
            icon={<FileTextOutlined />}
            onClick={() => handleViewDetail(record)}
            size="small"
          >
            Chi tiết
          </Button>
        </Space>
      )
    }
  ];

  // Calculate statistics
  const totalChecks = stockChecks.length;
  const completedChecks = stockChecks.filter(c => c.trang_thai === 'hoan_thanh').length;
  const inProgressChecks = stockChecks.filter(c => c.trang_thai === 'dang_thuc_hien').length;
  const totalDifference = stockChecks
    .filter(c => c.trang_thai === 'hoan_thanh')
    .reduce((sum, c) => sum + (c.gia_tri_lech || 0), 0);

  return (
    <div>
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng số phiếu"
              value={totalChecks}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đã hoàn thành"
              value={completedChecks}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đang kiểm kho"
              value={inProgressChecks}
              prefix={<ScanOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Chênh lệch tổng"
              value={totalDifference}
              prefix="₫"
              valueStyle={{ color: totalDifference >= 0 ? '#ff4d4f' : '#52c41a' }}
              formatter={(value) => value?.toLocaleString('vi-VN')}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Title level={4} style={{ margin: 0 }}>
                Kiểm kê kho hàng
              </Title>
            </Col>
            <Col flex="auto">
              <Space style={{ float: 'right' }}>
                <Input
                  placeholder="Tìm kiếm..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
                <Select
                  value={selectedWarehouse}
                  onChange={handleWarehouseFilter}
                  style={{ width: 150 }}
                >
                  <Option value="">Tất cả kho</Option>
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.ten_kho}
                    </Option>
                  ))}
                </Select>
                <Select
                  value={selectedStatus}
                  onChange={handleStatusFilter}
                  style={{ width: 150 }}
                >
                  <Option value="">Tất cả trạng thái</Option>
                  <Option value="ke_hoach">Kế hoạch</Option>
                  <Option value="dang_thuc_hien">Đang kiểm kho</Option>
                  <Option value="hoan_thanh">Đã cân bằng</Option>
                  <Option value="huy">Đã hủy</Option>
                </Select>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/warehouses/check/create')}
                >
                  Tạo phiếu kiểm kê
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={stockChecks}
          loading={stockChecksLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} phiếu kiểm kê`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size);
            },
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Create/Edit Check Modal */}
      <Modal
        title={modalType === 'create' ? 'Tạo phiếu kiểm kê mới' : 'Chỉnh sửa phiếu kiểm kê'}
        open={isModalVisible && (modalType === 'create' || modalType === 'edit')}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="ten_kiem_ke"
            label="Tên kiểm kê"
            rules={[{ required: true, message: 'Vui lòng nhập tên kiểm kê' }]}
          >
            <Input placeholder="Nhập tên kiểm kê" />
          </Form.Item>

          <Form.Item
            name="kho_hang_id"
            label="Kho hàng"
            rules={[{ required: true, message: 'Vui lòng chọn kho hàng' }]}
          >
            <Select placeholder="Chọn kho hàng">
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>
                  {warehouse.ten_kho}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ngay_bat_dau"
                label="Ngày bắt đầu"
                rules={[{ required: true, message: 'Vui lòng chọn ngày bắt đầu' }]}
              >
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ngay_ket_thuc"
                label="Ngày kết thúc (dự kiến)"
              >
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="ghi_chu"
            label="Ghi chú"
          >
            <Input.TextArea
              rows={3}
              placeholder="Nhập ghi chú về kiểm kê (tùy chọn)"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCancel}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                {modalType === 'create' ? 'Tạo phiếu kiểm kê' : 'Cập nhật phiếu kiểm kê'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title="Chi tiết kiểm kê"
        open={isModalVisible && modalType === 'view'}
        onCancel={handleCancel}
        footer={[
          <Button key="close" onClick={handleCancel}>
            Đóng
          </Button>
        ]}
        width={800}
      >
        {selectedRecord && modalType === 'view' && (
          <div>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="Mã kiểm kê"
                    value={selectedRecord.ma_kiem_ke}
                    valueStyle={{ fontSize: '16px' }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <div>
                    <Text type="secondary">Trạng thái</Text>
                    <br />
                    {getStatusTag(selectedRecord.trang_thai)}
                  </div>
                </Card>
              </Col>
            </Row>

            <Steps
              current={
                selectedRecord.trang_thai === 'ke_hoach' ? 0 :
                selectedRecord.trang_thai === 'dang_thuc_hien' ? 1 : 2
              }
              style={{ marginBottom: 24 }}
            >
              <Step title="Kế hoạch" description="Tạo phiếu kiểm kê" />
              <Step title="Thực hiện" description="Đang kiểm kê" />
              <Step title="Hoàn thành" description="Kết thúc kiểm kê" />
            </Steps>

            <Alert
              message="Thông tin kiểm kê"
              description={
                <div>
                  <p><strong>Tên:</strong> {selectedRecord.ten_kiem_ke}</p>
                  <p><strong>Kho:</strong> {selectedRecord.ten_kho}</p>
                  <p><strong>Người kiểm kê:</strong> {selectedRecord.nguoi_kiem_ke}</p>
                  <p><strong>Ngày bắt đầu:</strong> {dayjs(selectedRecord.ngay_bat_dau).format('DD/MM/YYYY')}</p>
                  {selectedRecord.ngay_ket_thuc && (
                    <p><strong>Ngày kết thúc:</strong> {dayjs(selectedRecord.ngay_ket_thuc).format('DD/MM/YYYY')}</p>
                  )}
                  {selectedRecord.ghi_chu && (
                    <p><strong>Ghi chú:</strong> {selectedRecord.ghi_chu}</p>
                  )}
                </div>
              }
              type="info"
              style={{ marginBottom: 16 }}
            />

            {selectedRecord.trang_thai !== 'ke_hoach' && (
              <Row gutter={16}>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="Tổng sản phẩm"
                      value={selectedRecord.tong_san_pham || 0}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="Sản phẩm lệch"
                      value={selectedRecord.san_pham_lech || 0}
                      valueStyle={{ color: '#ff4d4f' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="Giá trị lệch"
                      value={selectedRecord.gia_tri_lech || 0}
                      prefix="₫"
                      valueStyle={{ color: selectedRecord.gia_tri_lech >= 0 ? '#ff4d4f' : '#52c41a' }}
                      formatter={(value) => value?.toLocaleString('vi-VN')}
                    />
                  </Card>
                </Col>
              </Row>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default StockCheck;
