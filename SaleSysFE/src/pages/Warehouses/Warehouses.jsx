import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Tooltip,
  Spin
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  HomeOutlined,
  InboxOutlined,
  ShoppingOutlined,
  EyeOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import {
  useWarehouses,
  useDeleteWarehouse
} from '../../hooks/useWarehouses';

const { Title, Text } = Typography;

const Warehouses = () => {
  const [searchText, setSearchText] = useState('');
  const navigate = useNavigate();

  // API hooks
  const { data: warehouses = [], isLoading, error } = useWarehouses();
  const deleteWarehouseMutation = useDeleteWarehouse();

  // Debug logging
  console.log('🏪 Warehouses Component Debug:');
  console.log('- warehouses:', warehouses);
  console.log('- warehouses type:', typeof warehouses);
  console.log('- warehouses length:', Array.isArray(warehouses) ? warehouses.length : 'Not array');
  console.log('- isLoading:', isLoading);
  console.log('- error:', error);

  // Filter warehouses based on search
  const filteredWarehouses = useMemo(() => {
    console.log('🔍 Filtering warehouses:');
    console.log('- Input warehouses:', warehouses);
    console.log('- Is array:', Array.isArray(warehouses));

    if (!Array.isArray(warehouses)) {
      console.log('❌ Warehouses is not array, returning empty array');
      return [];
    }
    if (!searchText) {
      console.log('✅ No search text, returning all warehouses:', warehouses.length);
      return warehouses;
    }

    const filtered = warehouses.filter(warehouse =>
      warehouse?.ten_kho?.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse?.dia_chi?.toLowerCase().includes(searchText.toLowerCase())
    );
    console.log('✅ Filtered warehouses:', filtered.length);
    return filtered;
  }, [searchText, warehouses]);

  const handleAdd = () => {
    navigate('/warehouses/add');
  };

  const handleEdit = (warehouse) => {
    navigate(`/warehouses/edit/${warehouse.id}`);
  };

  const handleView = (warehouse) => {
    navigate(`/warehouses/view/${warehouse.id}`);
  };

  const handleDelete = async (id) => {
    deleteWarehouseMutation.mutate(id);
  };

  const columns = [
    {
      title: 'Tên kho',
      dataIndex: 'ten_kho',
      key: 'ten_kho',
      width: 200,
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ID: {record.id}
          </Text>
        </div>
      )
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'dia_chi',
      key: 'dia_chi',
      ellipsis: true
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'tong_san_pham',
      key: 'tong_san_pham',
      width: 120,
      align: 'center',
      render: (value) => (
        <Tooltip title="Tổng số loại sản phẩm">
          <Tag color="blue" icon={<ShoppingOutlined />}>
            {value}
          </Tag>
        </Tooltip>
      )
    },
    {
      title: 'Tồn kho',
      dataIndex: 'tong_ton_kho',
      key: 'tong_ton_kho',
      width: 120,
      align: 'center',
      render: (value) => (
        <Tooltip title="Tổng số lượng tồn kho">
          <Tag color="green" icon={<InboxOutlined />}>
            {value?.toLocaleString('vi-VN')}
          </Tag>
        </Tooltip>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 120,
      align: 'center',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? 'Hoạt động' : 'Tạm dừng'}
        </Tag>
      )
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'ngay_tao',
      key: 'ngay_tao',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString('vi-VN')
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa kho hàng này?"
              onConfirm={() => handleDelete(record.id)}
              okText="Xóa"
              cancelText="Hủy"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  // Calculate statistics
  const safeWarehouses = Array.isArray(warehouses) ? warehouses : [];
  const totalWarehouses = safeWarehouses.length;
  const activeWarehouses = safeWarehouses.filter(w => w?.trang_thai === 'active').length;
  const totalProducts = safeWarehouses.reduce((sum, w) => sum + (w?.tong_san_pham || 0), 0);
  const totalStock = safeWarehouses.reduce((sum, w) => sum + (w?.tong_ton_kho || 0), 0);

  // Show loading spinner
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Show error message
  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '48px' }}>
          <h3>Có lỗi xảy ra khi tải dữ liệu</h3>
          <p>{error.message}</p>
          <Button type="primary" onClick={() => window.location.reload()}>
            Thử lại
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng số kho"
              value={totalWarehouses}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Kho hoạt động"
              value={activeWarehouses}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng sản phẩm"
              value={totalProducts}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng tồn kho"
              value={totalStock}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#fa8c16' }}
              formatter={(value) => value?.toLocaleString('vi-VN')}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>
            Danh sách kho hàng
          </Title>
          <Space>
            <Input
              placeholder="Tìm kiếm kho hàng..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              Thêm kho hàng
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredWarehouses}
          loading={isLoading || deleteWarehouseMutation.isLoading}
          rowKey="id"
          pagination={{
            total: filteredWarehouses.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} kho hàng`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>


    </div>
  );
};

export default Warehouses;
