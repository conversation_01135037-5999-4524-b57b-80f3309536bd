import React, { useEffect } from 'react';
import { Card, Form, Input, Switch, Button, Row, Col, message, Breadcrumb, Spin, Statistic } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  ShopOutlined, 
  EnvironmentOutlined, 
  FileTextOutlined, 
  SaveOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  CheckCircleOutlined,
  InboxOutlined
} from '@ant-design/icons';
import { useWarehouse, useUpdateWarehouse } from '../../hooks/useWarehouses';

const { TextArea } = Input;

const EditWarehouse = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams();
  
  const { data: warehouse, isLoading, error } = useWarehouse(id);
  const updateWarehouseMutation = useUpdateWarehouse();

  useEffect(() => {
    if (warehouse) {
      form.setFieldsValue({
        ten_kho: warehouse.ten_kho,
        dia_chi: warehouse.dia_chi,
        mo_ta: warehouse.mo_ta,
        trang_thai: warehouse.trang_thai === 'active'
      });
    }
  }, [warehouse, form]);

  const handleSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        trang_thai: values.trang_thai ? 'active' : 'inactive'
      };
      
      await updateWarehouseMutation.mutateAsync({
        id: id,
        data: submitData
      });
      message.success('Cập nhật kho hàng thành công!');
      navigate('/warehouses');
    } catch (error) {
      console.error('Error updating warehouse:', error);
      message.error('Có lỗi xảy ra khi cập nhật kho hàng!');
    }
  };

  const handleCancel = () => {
    navigate('/warehouses');
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !warehouse) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '48px' }}>
          <h3>Không tìm thấy kho hàng</h3>
          <p>Kho hàng không tồn tại hoặc đã bị xóa.</p>
          <Button type="primary" onClick={() => navigate('/warehouses')}>
            Quay lại danh sách
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: 24 }}>
        <Breadcrumb.Item>
          <HomeOutlined />
          <span>Trang chủ</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <ShopOutlined />
          <span>Kho hàng</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>Sửa kho hàng</Breadcrumb.Item>
      </Breadcrumb>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Tổng sản phẩm"
              value={warehouse.tong_san_pham || 0}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Tổng tồn kho"
              value={warehouse.tong_ton_kho || 0}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Trạng thái hiện tại"
              value={warehouse.trang_thai === 'active' ? 'Hoạt động' : 'Ngừng hoạt động'}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ 
                color: warehouse.trang_thai === 'active' ? '#52c41a' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <ShopOutlined />
            Sửa thông tin kho hàng: {warehouse.ten_kho}
          </div>
        }
        extra={
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleCancel}
          >
            Quay lại
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ maxWidth: 800 }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Tên kho hàng"
                name="ten_kho"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên kho hàng!' },
                  { min: 2, message: 'Tên kho hàng phải có ít nhất 2 ký tự!' },
                  { max: 100, message: 'Tên kho hàng không được quá 100 ký tự!' }
                ]}
              >
                <Input
                  prefix={<ShopOutlined />}
                  placeholder="Nhập tên kho hàng"
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Trạng thái"
                name="trang_thai"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="Hoạt động"
                  unCheckedChildren="Ngừng hoạt động"
                  size="default"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Địa chỉ"
            name="dia_chi"
            rules={[
              { required: true, message: 'Vui lòng nhập địa chỉ kho hàng!' },
              { min: 5, message: 'Địa chỉ phải có ít nhất 5 ký tự!' },
              { max: 255, message: 'Địa chỉ không được quá 255 ký tự!' }
            ]}
          >
            <Input
              prefix={<EnvironmentOutlined />}
              placeholder="Nhập địa chỉ kho hàng"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="Mô tả"
            name="mo_ta"
            rules={[
              { max: 500, message: 'Mô tả không được quá 500 ký tự!' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Nhập mô tả về kho hàng (tùy chọn)"
              showCount
              maxLength={500}
            />
          </Form.Item>

          {/* Action Buttons */}
          <Form.Item style={{ marginTop: 32 }}>
            <Row gutter={16}>
              <Col>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  size="large"
                  loading={updateWarehouseMutation.isLoading}
                >
                  Cập nhật kho hàng
                </Button>
              </Col>
              <Col>
                <Button
                  size="large"
                  onClick={handleCancel}
                  disabled={updateWarehouseMutation.isLoading}
                >
                  Hủy
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default EditWarehouse;
