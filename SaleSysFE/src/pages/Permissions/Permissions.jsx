import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Card,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';

import { permissionsAPI } from '../../services/api';
import { usePermissions } from '../../contexts/PermissionContext';

const { Title } = Typography;
const { TextArea } = Input;

const Permissions = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState(null);
  const [form] = Form.useForm();

  const { isAdmin } = usePermissions();
  const queryClient = useQueryClient();

  // Fetch permissions
  const { data: permissionsData, isLoading, refetch } = useQuery(
    'permissions', 
    permissionsAPI.getPermissions
  );

  // Create permission mutation
  const createPermissionMutation = useMutation(permissionsAPI.createPermission, {
    onSuccess: () => {
      message.success('Tạo quyền thành công!');
      setIsModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries('permissions');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo quyền');
    }
  });

  // Update permission mutation
  const updatePermissionMutation = useMutation(
    ({ id, data }) => permissionsAPI.updatePermission(id, data),
    {
      onSuccess: () => {
        message.success('Cập nhật quyền thành công!');
        setIsModalVisible(false);
        setEditingPermission(null);
        form.resetFields();
        queryClient.invalidateQueries('permissions');
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật quyền');
      }
    }
  );

  // Delete permission mutation
  const deletePermissionMutation = useMutation(permissionsAPI.deletePermission, {
    onSuccess: () => {
      message.success('Xóa quyền thành công!');
      queryClient.invalidateQueries('permissions');
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa quyền');
    }
  });

  const handleCreate = () => {
    setEditingPermission(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingPermission(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = (id) => {
    deletePermissionMutation.mutate(id);
  };

  const handleSubmit = async (values) => {
    try {
      if (editingPermission) {
        await updatePermissionMutation.mutateAsync({
          id: editingPermission.id,
          data: values
        });
      } else {
        await createPermissionMutation.mutateAsync(values);
      }
    } catch (error) {
      // Error handled in mutation
    }
  };

  const getPermissionCategory = (maQuyen) => {
    if (maQuyen.includes('NGUOI_DUNG')) return { color: 'blue', text: 'Người dùng' };
    if (maQuyen.includes('VAI_TRO')) return { color: 'purple', text: 'Vai trò' };
    if (maQuyen.includes('QUYEN')) return { color: 'orange', text: 'Quyền' };
    if (maQuyen.includes('SAN_PHAM')) return { color: 'green', text: 'Sản phẩm' };
    if (maQuyen.includes('DON_HANG')) return { color: 'cyan', text: 'Đơn hàng' };
    if (maQuyen.includes('KHACH_HANG')) return { color: 'magenta', text: 'Khách hàng' };
    if (maQuyen.includes('KHO_HANG')) return { color: 'volcano', text: 'Kho hàng' };
    if (maQuyen.includes('BAO_CAO')) return { color: 'gold', text: 'Báo cáo' };
    return { color: 'default', text: 'Khác' };
  };

  const columns = [
    {
      title: 'Mã quyền',
      dataIndex: 'ma_quyen',
      key: 'ma_quyen',
      render: (text) => <Tag color="red">{text}</Tag>,
    },
    {
      title: 'Tên quyền',
      dataIndex: 'ten_quyen',
      key: 'ten_quyen',
    },
    {
      title: 'Danh mục',
      dataIndex: 'ma_quyen',
      key: 'category',
      render: (maQuyen) => {
        const { color, text } = getPermissionCategory(maQuyen);
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta',
      ellipsis: true,
    },
    {
      title: 'Số vai trò sử dụng',
      dataIndex: 'vaiTroList',
      key: 'vaiTroList',
      render: (roles) => (
        <Tag color="green">{roles?.length || 0} vai trò</Tag>
      ),
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          {isAdmin() && (
            <>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                size="small"
              />
              <Popconfirm
                title="Bạn có chắc chắn muốn xóa quyền này?"
                onConfirm={() => handleDelete(record.id)}
                okText="Có"
                cancelText="Không"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                />
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                Quản lý quyền
              </Title>
            </Col>
            <Col>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                  loading={isLoading}
                >
                  Làm mới
                </Button>
                {isAdmin() && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleCreate}
                  >
                    Thêm quyền
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={permissionsData?.data?.data?.permissions || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} quyền`,
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingPermission ? 'Chỉnh sửa quyền' : 'Thêm quyền mới'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingPermission(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ma_quyen"
                label="Mã quyền"
                rules={[{ required: true, message: 'Vui lòng nhập mã quyền!' }]}
              >
                <Input 
                  placeholder="Nhập mã quyền (VD: XEM_SAN_PHAM)" 
                  style={{ textTransform: 'uppercase' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ten_quyen"
                label="Tên quyền"
                rules={[{ required: true, message: 'Vui lòng nhập tên quyền!' }]}
              >
                <Input placeholder="Nhập tên quyền" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="mo_ta"
            label="Mô tả"
          >
            <TextArea 
              rows={3} 
              placeholder="Nhập mô tả quyền" 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createPermissionMutation.isLoading || updatePermissionMutation.isLoading}
              >
                {editingPermission ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Permissions;
