# Trang Tạo Sản Phẩm - Hướng Dẫn Sử Dụng

## 🎯 Tổng Quan

Trang tạo sản phẩm được thiết kế để tạo sản phẩm với nhiều phiên bản dựa trên thuộc tính. <PERSON>hi bạn thêm thuộc t<PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>), hệ thống sẽ tự động tạo ra tất cả các tổ hợp phiên bản có thể.

## 📋 Các Tính Năng Chính

### 1. **Thông Tin Cơ Bản**
- **Tên sản phẩm**: Bắt buộc nhập
- **Loại sản phẩm**: Chọn từ dropdown
- **Mô tả**: Mô tả chi tiết sản phẩm (tối đa 1000 ký tự)

### 2. **Quản Lý Ảnh**
- Upload nhiều ảnh bằng drag & drop
- Preview ảnh trước khi lưu
- Hỗ trợ các định dạng: <PERSON><PERSON>, PNG, GIF

### 3. **Thuộc T<PERSON>h Sản Phẩm** ⭐
- **Thêm thuộc tính**: Click "Thêm thuộc tính khác" và nhập tên (VD: Size, Màu sắc)
- **Thêm giá trị**: Gõ giá trị và nhấn Enter (VD: S, M, L cho Size)
- **Tự động tạo phiên bản**: Hệ thống sẽ tự động tạo tất cả tổ hợp

### 4. **Phiên Bản Sản Phẩm**
- Hiển thị dạng bảng với các cột:
  - ✅ Checkbox để chọn/bỏ chọn
  - 📷 Ảnh phiên bản
  - Tên phiên bản (có thể chỉnh sửa)
  - Mã SKU
  - Barcode
  - Giá bán lẻ, buôn, nhập
- **Chỉnh sửa trực tiếp**: Click vào ô để chỉnh sửa giá trị

### 5. **Thông Tin Bổ Sung**
- **Nhãn hiệu**: Chọn từ danh sách có sẵn
- **Tag**: Gắn tag cho sản phẩm
- **Đơn vị quy đổi**: Tạo quy đổi giữa các đơn vị

### 6. **Khởi Tạo Kho Hàng**
- Bật/tắt tính năng khởi tạo tồn kho
- Thiết lập số lượng ban đầu cho từng kho

## 🔄 Quy Trình Tạo Sản Phẩm

### Bước 1: Nhập Thông Tin Cơ Bản
```
1. Nhập tên sản phẩm: "Áo Thun Cotton"
2. Chọn loại sản phẩm: "Áo thun"
3. Nhập mô tả sản phẩm
```

### Bước 2: Thêm Thuộc Tính
```
1. Click "Thêm thuộc tính khác"
2. Nhập "Size" → Enter
3. Thêm giá trị: "S" → Enter, "M" → Enter, "L" → Enter
4. Thêm thuộc tính "Màu sắc"
5. Thêm giá trị: "Đỏ" → Enter, "Xanh" → Enter
```

### Bước 3: Hệ Thống Tự Động Tạo Phiên Bản
```
Kết quả: 6 phiên bản được tạo tự động
- Áo Thun Cotton - S - Đỏ
- Áo Thun Cotton - S - Xanh  
- Áo Thun Cotton - M - Đỏ
- Áo Thun Cotton - M - Xanh
- Áo Thun Cotton - L - Đỏ
- Áo Thun Cotton - L - Xanh
```

### Bước 4: Chỉnh Sửa Phiên Bản
```
1. Click vào ô "Giá bán lẻ" để nhập giá
2. Nhập mã SKU cho từng phiên bản
3. Thêm barcode nếu có
4. Upload ảnh cho từng phiên bản
```

### Bước 5: Lưu Sản Phẩm
```
1. Click "Lưu" để tạo sản phẩm
2. Hoặc "Lưu và tạo mới tiếp" để tiếp tục tạo sản phẩm khác
```

## 💡 Mẹo Sử Dụng

### ✅ Nên Làm:
- Nhập tên sản phẩm trước khi thêm thuộc tính
- Thêm thuộc tính theo thứ tự quan trọng (Size trước, Màu sau)
- Kiểm tra kỹ giá trị thuộc tính trước khi thêm
- Đặt giá cho tất cả phiên bản trước khi lưu

### ❌ Tránh:
- Thêm quá nhiều thuộc tính (sẽ tạo ra quá nhiều phiên bản)
- Nhập sai tên thuộc tính (khó sửa sau)
- Bỏ trống giá bán cho các phiên bản quan trọng

## 🧮 Công Thức Tính Số Phiên Bản

```
Số phiên bản = Số giá trị thuộc tính 1 × Số giá trị thuộc tính 2 × ...

Ví dụ:
- Size: 3 giá trị (S, M, L)
- Màu: 2 giá trị (Đỏ, Xanh)
- Chất liệu: 2 giá trị (Cotton, Polyester)
→ Tổng: 3 × 2 × 2 = 12 phiên bản
```

## 🔧 Xử Lý Sự Cố

### Lỗi: "Không thể tạo phiên bản"
- **Nguyên nhân**: Chưa có thuộc tính nào
- **Giải pháp**: Thêm ít nhất 1 thuộc tính với 1 giá trị

### Lỗi: "Quá nhiều phiên bản"
- **Nguyên nhân**: Tổ hợp thuộc tính tạo ra quá nhiều phiên bản
- **Giải pháp**: Giảm số lượng giá trị thuộc tính

### Phiên bản không cập nhật tên
- **Nguyên nhân**: Thay đổi tên sản phẩm sau khi tạo phiên bản
- **Giải pháp**: Chỉnh sửa tên phiên bản thủ công trong bảng

## 📱 Responsive Design

Trang được thiết kế responsive, hoạt động tốt trên:
- 💻 Desktop (1200px+)
- 📱 Tablet (768px - 1199px)  
- 📱 Mobile (< 768px)

## 🔐 Quyền Truy Cập

Cần có quyền `THEM_SAN_PHAM` để truy cập trang này.

## 🚀 Phím Tắt

- **Enter**: Thêm giá trị thuộc tính
- **Tab**: Di chuyển giữa các ô trong bảng phiên bản
- **Ctrl + S**: Lưu sản phẩm (sẽ được thêm trong tương lai)

---

*Cập nhật lần cuối: Tháng 6, 2024*
