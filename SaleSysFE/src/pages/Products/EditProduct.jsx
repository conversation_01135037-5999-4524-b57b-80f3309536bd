import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  message,
  Spin,
  Alert,
  Row,
  Col,
  Typography,
  Table,
  InputNumber,
  Tag,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import {
  useProduct,
  useCategories,
  useBrands,
  useTags,
  useAttributes,
  useUpdateProduct
} from '../../hooks/useProducts';
import { useWarehouses } from '../../hooks/useWarehouses';
import ImageUpload from '../../components/ImageUpload/ImageUpload';
import ImageSelector from '../../components/ImageSelector/ImageSelector';
import InlineWarehouseManager from '../../components/Products/InlineWarehouseManager';
import InlineAttributeManager from '../../components/Products/InlineAttributeManager';
import InlineUnitConversionManager from '../../components/Products/InlineUnitConversionManager';
import VariantModal from '../../components/Products/VariantModal';
import AttributeModal from '../../components/Products/AttributeModal';
import UnitConversionModal from '../../components/Products/UnitConversionModal';
import styles from './CreateProduct.module.css';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const EditProduct = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // State for product data
  const [productImages, setProductImages] = useState([]);
  const [variants, setVariants] = useState([]);
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [selectedVariantIndex, setSelectedVariantIndex] = useState(null);

  // State for attributes
  const [productAttributes, setProductAttributes] = useState([]);
  const [showAttributeModal, setShowAttributeModal] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState(null);

  // State for variants modal
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [editingVariant, setEditingVariant] = useState(null);

  // State for inventory
  const [enableInventory, setEnableInventory] = useState(false);
  const [inventoryData, setInventoryData] = useState({});

  // State for unit conversions
  const [enableUnitConversion, setEnableUnitConversion] = useState(false);
  const [inlineUnitConversions, setInlineUnitConversions] = useState([]);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [editingUnit, setEditingUnit] = useState(null);

  // API hooks
  const { data: product, isLoading: productLoading, error: productError } = useProduct(id);
  const { data: categories = [], isLoading: categoriesLoading } = useCategories();
  const { data: brands = [], isLoading: brandsLoading } = useBrands();
  const { data: tags = [], isLoading: tagsLoading } = useTags();
  const { data: attributes = [], isLoading: attributesLoading } = useAttributes();
  const { data: warehouses = [], isLoading: warehousesLoading } = useWarehouses();
  const updateProductMutation = useUpdateProduct();

  // Set form values and state when product data is loaded
  useEffect(() => {
    if (product) {
      // Set form values
      form.setFieldsValue({
        ten: product.ten,
        ma: product.ma,
        mo_ta: product.mo_ta,
        loai_san_pham_id: product.loai_san_pham_id,
        nhan_hieu_id: product.nhan_hieu_id,
        tag_id: product.tag_id,
        trang_thai: product.trang_thai
      });

      // Set product images
      if (product.anhList && product.anhList.length > 0) {
        const images = product.anhList.map(img => ({
          uid: img.id,
          name: img.ten_file || `image-${img.id}`,
          status: 'done',
          url: img.url,
          optimized_url: img.optimized_url,
          thumbnail_url: img.thumbnail_url,
          public_id: img.public_id
        }));
        setProductImages(images);
      }

      // Set variants
      if (product.phienBanList && product.phienBanList.length > 0) {
        const variantData = product.phienBanList.map(variant => ({
          id: variant.id,
          ten_phien_ban: variant.ten_phien_ban,
          ma_phien_ban: variant.ma,
          gia_le: variant.gia_le,
          gia_buon: variant.gia_buon,
          gia_nhap: variant.gia_nhap,
          don_vi_tinh: variant.don_vi_tinh,
          anh_phien_ban: productImages.length > 0 ? [productImages[0]] : [],
          attributes: variant.giaTriThuocTinhList || []
        }));
        setVariants(variantData);
      }

      // Set product attributes (if any)
      if (product.thuocTinhList && product.thuocTinhList.length > 0) {
        const attributeData = product.thuocTinhList.map(attr => ({
          id: attr.id,
          name: attr.ten,
          values: attr.giaTriList?.map(gt => gt.gia_tri) || []
        }));
        setProductAttributes(attributeData);
      }

      // Set inventory data (if any)
      // This would need to be loaded separately from inventory API

      // Set unit conversions (if any)
      // This would need to be loaded separately from unit conversion API
    }
  }, [product, form]);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // Prepare data for submission
      const submitData = {
        ...values,
        phien_ban: variants.map(variant => ({
          id: variant.id, // Include ID for existing variants
          ten_phien_ban: variant.ten_phien_ban,
          ma: variant.ma_phien_ban,
          gia_le: variant.gia_le,
          gia_buon: variant.gia_buon,
          gia_nhap: variant.gia_nhap,
          don_vi_tinh: variant.don_vi_tinh
        })),
        anh_san_pham: productImages.map(img => ({
          url: img.url,
          public_id: img.public_id,
          thumbnail_url: img.thumbnail_url,
          optimized_url: img.optimized_url
        })),
        thuoc_tinh: productAttributes,
        inventory_data: enableInventory ? inventoryData : null,
        unit_conversions: enableUnitConversion ? inlineUnitConversions : null
      };

      await updateProductMutation.mutateAsync({
        id,
        data: submitData
      });

      message.success('Cập nhật sản phẩm thành công!');
      navigate(`/products/detail/${id}`);
    } catch (error) {
      message.error(error.message || 'Có lỗi xảy ra khi cập nhật sản phẩm');
    } finally {
      setLoading(false);
    }
  };

  // Handle product images change
  const handleProductImagesChange = (newImages) => {
    setProductImages(newImages);
  };

  // Handle image selection for variant
  const handleImageSelect = (image) => {
    if (selectedVariantIndex !== null) {
      const newVariants = [...variants];
      newVariants[selectedVariantIndex].anh_phien_ban = image ? [image] : [];
      setVariants(newVariants);
    }
    setShowImageSelector(false);
    setSelectedVariantIndex(null);
  };

  // Add new variant
  const addVariant = () => {
    const newVariant = {
      id: null, // New variant
      ten_phien_ban: `Phiên bản ${variants.length + 1}`,
      ma_phien_ban: '',
      gia_le: 0,
      gia_buon: 0,
      gia_nhap: 0,
      don_vi_tinh: 'cái',
      anh_phien_ban: productImages.length > 0 ? [productImages[0]] : [],
      attributes: []
    };
    setVariants([...variants, newVariant]);
  };

  // Remove variant
  const removeVariant = (index) => {
    const newVariants = variants.filter((_, i) => i !== index);
    setVariants(newVariants);
  };

  // Update variant
  const updateVariant = (index, field, value) => {
    const newVariants = [...variants];
    newVariants[index][field] = value;
    setVariants(newVariants);
  };

  // Generate variants from attributes
  const generateVariantsFromAttributes = (newAttributes) => {
    if (newAttributes.length === 0) {
      return;
    }

    // Helper function to generate all combinations
    const generateCombinations = (attributeValues) => {
      if (attributeValues.length === 0) return [];
      if (attributeValues.length === 1) {
        return attributeValues[0].values.map(value => [{
          attribute: attributeValues[0].name,
          value: value
        }]);
      }

      const result = [];
      const firstAttribute = attributeValues[0];
      const restCombinations = generateCombinations(attributeValues.slice(1));

      firstAttribute.values.forEach(value => {
        restCombinations.forEach(combination => {
          result.push([
            { attribute: firstAttribute.name, value: value },
            ...combination
          ]);
        });
      });

      return result;
    };

    const combinations = generateCombinations(newAttributes);
    const newVariants = combinations.map((combination, index) => {
      const variantName = combination.map(attr => attr.value).join(' - ');
      return {
        id: null, // New variant
        ten_phien_ban: variantName,
        ma_phien_ban: '',
        gia_le: 0,
        gia_buon: 0,
        gia_nhap: 0,
        don_vi_tinh: 'cái',
        anh_phien_ban: productImages.length > 0 ? [productImages[0]] : [],
        attributes: combination
      };
    });

    setVariants(newVariants);
  };

  // Attribute handlers
  const saveAttribute = (attributeData) => {
    if (editingAttribute !== null) {
      // Edit existing
      const newAttributes = [...productAttributes];
      newAttributes[editingAttribute] = attributeData;
      setProductAttributes(newAttributes);
      generateVariantsFromAttributes(newAttributes);
    } else {
      // Add new
      const newAttributes = [...productAttributes, attributeData];
      setProductAttributes(newAttributes);
      generateVariantsFromAttributes(newAttributes);
    }
    setShowAttributeModal(false);
    setEditingAttribute(null);
  };

  // Variant modal handlers
  const saveVariant = (variantData) => {
    if (editingVariant !== null) {
      // Edit existing
      const newVariants = [...variants];
      newVariants[editingVariant] = { ...newVariants[editingVariant], ...variantData };
      setVariants(newVariants);
    } else {
      // Add new
      setVariants([...variants, {
        id: null,
        ...variantData,
        anh_phien_ban: productImages.length > 0 ? [productImages[0]] : []
      }]);
    }
    setShowVariantModal(false);
    setEditingVariant(null);
  };

  // Unit conversion handlers
  const saveUnitConversion = (unitData) => {
    if (editingUnit !== null) {
      // Edit existing
      const newUnits = [...inlineUnitConversions];
      newUnits[editingUnit] = unitData;
      setInlineUnitConversions(newUnits);
    } else {
      // Add new
      setInlineUnitConversions([...inlineUnitConversions, unitData]);
    }
    setShowUnitModal(false);
    setEditingUnit(null);
  };

  // Variants table columns
  const variantColumns = [
    {
      title: 'Ảnh',
      dataIndex: 'anh_phien_ban',
      key: 'anh_phien_ban',
      width: 80,
      render: (images, record, index) => (
        <div
          style={{
            width: 60,
            height: 60,
            border: '1px dashed #d9d9d9',
            borderRadius: 6,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            background: images && images.length > 0 ? 'transparent' : '#fafafa'
          }}
          onClick={() => {
            setSelectedVariantIndex(index);
            setShowImageSelector(true);
          }}
        >
          {images && images.length > 0 ? (
            <img
              src={images[0].thumbnail_url || images[0].url}
              alt="Variant"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: 4
              }}
            />
          ) : (
            <EyeOutlined style={{ fontSize: 20, color: '#999' }} />
          )}
        </div>
      )
    },
    {
      title: 'Tên phiên bản',
      dataIndex: 'ten_phien_ban',
      key: 'ten_phien_ban',
      width: 200,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateVariant(index, 'ten_phien_ban', e.target.value)}
          placeholder="Nhập tên phiên bản"
          size="small"
        />
      )
    },
    {
      title: 'Mã phiên bản',
      dataIndex: 'ma_phien_ban',
      key: 'ma_phien_ban',
      width: 150,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateVariant(index, 'ma_phien_ban', e.target.value)}
          placeholder="Mã tự động"
          size="small"
        />
      )
    },
    {
      title: 'Giá bán lẻ (VNĐ)',
      dataIndex: 'gia_le',
      key: 'gia_le',
      width: 150,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => updateVariant(index, 'gia_le', val || 0)}
          placeholder="0"
          min={0}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          size="small"
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Giá bán buôn (VNĐ)',
      dataIndex: 'gia_buon',
      key: 'gia_buon',
      width: 150,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => updateVariant(index, 'gia_buon', val || 0)}
          placeholder="0"
          min={0}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          size="small"
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Giá nhập (VNĐ)',
      dataIndex: 'gia_nhap',
      key: 'gia_nhap',
      width: 150,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => updateVariant(index, 'gia_nhap', val || 0)}
          placeholder="0"
          min={0}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          size="small"
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn vị tính',
      dataIndex: 'don_vi_tinh',
      key: 'don_vi_tinh',
      width: 120,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateVariant(index, 'don_vi_tinh', e.target.value)}
          placeholder="cái"
          size="small"
        />
      )
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 80,
      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeVariant(index)}
          size="small"
          title="Xóa phiên bản"
        />
      )
    }
  ];

  // Loading states
  const isLoading = productLoading || categoriesLoading || brandsLoading || tagsLoading || attributesLoading || warehousesLoading;

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>Đang tải thông tin sản phẩm...</p>
      </div>
    );
  }

  if (productError) {
    return (
      <Alert
        message="Lỗi"
        description={productError.message || 'Không thể tải thông tin sản phẩm'}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  if (!product) {
    return (
      <Alert
        message="Không tìm thấy sản phẩm"
        description="Sản phẩm không tồn tại hoặc đã bị xóa"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  return (
    <div className={styles.createProductContainer}>
      {/* Header */}
      <div className={styles.header}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(`/products/detail/${id}`)}
          className={styles.backButton}
        >
          Quay lại chi tiết sản phẩm
        </Button>

        <div className={styles.headerContent}>
          <Title level={2} className={styles.title}>
            Chỉnh sửa sản phẩm: {product.ten}
          </Title>

          <div className={styles.actionButtons}>
            <Button onClick={() => navigate(`/products/detail/${id}`)}>
              Hủy
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => form.submit()}
              loading={loading}
            >
              Cập nhật sản phẩm
            </Button>
          </div>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={loading}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Basic Information */}
            <Card title="Thông tin cơ bản" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Tên sản phẩm"
                    name="ten"
                    rules={[
                      { required: true, message: 'Vui lòng nhập tên sản phẩm!' }
                    ]}
                  >
                    <Input placeholder="Nhập tên sản phẩm" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Mã sản phẩm"
                    name="ma"
                  >
                    <Input placeholder="Nhập mã sản phẩm (tùy chọn)" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Mô tả sản phẩm"
                name="mo_ta"
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập mô tả sản phẩm (tùy chọn)"
                />
              </Form.Item>
            </Card>

            {/* Common Pricing */}
            <Card title="Giá chung cho phiên bản" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="gia_ban_le_chung"
                    label={
                      <span>
                        Giá bán lẻ
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                  >
                    <InputNumber
                      placeholder="0"
                      min={0}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      style={{ width: '100%' }}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_le: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="gia_ban_buon_chung"
                    label={
                      <span>
                        Giá bán buôn
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                  >
                    <InputNumber
                      placeholder="0"
                      min={0}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      style={{ width: '100%' }}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_buon: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="gia_nhap_chung"
                    label={
                      <span>
                        Giá nhập
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                  >
                    <InputNumber
                      placeholder="0"
                      min={0}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      style={{ width: '100%' }}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_nhap: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{
                fontSize: '13px',
                color: '#666',
                background: '#f9f9f9',
                padding: 8,
                borderRadius: 4
              }}>
                <span style={{ fontWeight: 'bold' }}>💡 Lưu ý:</span> Giá này sẽ được áp dụng mặc định cho tất cả phiên bản mới.
                Bạn có thể chỉnh sửa giá riêng cho từng phiên bản trong bảng bên dưới.
              </div>
            </Card>

            {/* Product Images */}
            <Card title="Ảnh sản phẩm" style={{ marginBottom: 24 }}>
              <ImageUpload
                value={productImages}
                onChange={handleProductImagesChange}
                maxCount={10}
                folder="products"
                width={800}
                height={600}
              />
              <div style={{
                marginTop: 12,
                fontSize: '13px',
                color: '#666',
                background: '#f9f9f9',
                padding: 8,
                borderRadius: 4
              }}>
                💡 <strong>Lưu ý:</strong> Ảnh đầu tiên sẽ được sử dụng làm ảnh đại diện mặc định cho các phiên bản sản phẩm.
                Bạn có thể click vào ảnh đại diện của từng phiên bản để chọn ảnh khác.
              </div>
            </Card>

            {/* Inventory Settings */}
            <InlineWarehouseManager
              enabled={enableInventory}
              onEnabledChange={setEnableInventory}
              warehouses={warehouses}
              inventoryData={inventoryData}
              onInventoryChange={setInventoryData}
            />

            {/* Attributes */}
            <InlineAttributeManager
              attributes={productAttributes}
              onAttributesChange={(newAttributes) => {
                setProductAttributes(newAttributes);
                generateVariantsFromAttributes(newAttributes);
              }}
            />

            {/* Product Variants */}
            <Card
              title={`Phiên bản sản phẩm (${variants.length})`}
              style={{ marginBottom: 24 }}
              extra={
                variants.length > 0 && (
                  <Space>
                    <Button
                      icon={<PlusOutlined />}
                      onClick={addVariant}
                      size="small"
                    >
                      Thêm phiên bản
                    </Button>
                    <Button
                      type="primary"
                      ghost
                      size="small"
                      onClick={() => {
                        const commonRetailPrice = form.getFieldValue('gia_ban_le_chung') || 0;
                        const commonWholesalePrice = form.getFieldValue('gia_ban_buon_chung') || 0;
                        const commonImportPrice = form.getFieldValue('gia_nhap_chung') || 0;

                        const newVariants = variants.map(variant => ({
                          ...variant,
                          gia_le: commonRetailPrice,
                          gia_buon: commonWholesalePrice,
                          gia_nhap: commonImportPrice
                        }));
                        setVariants(newVariants);
                        message.success('Đã áp dụng giá chung cho tất cả phiên bản!');
                      }}
                      style={{ color: '#52c41a', borderColor: '#52c41a' }}
                    >
                      💰 Áp dụng giá chung
                    </Button>
                  </Space>
                )
              }
            >
              {variants.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  <Text>Chưa có phiên bản nào. Click "Thêm phiên bản" để tạo mới.</Text>
                </div>
              ) : (
                <div style={{ overflowX: 'auto' }}>
                  <Table
                    dataSource={variants}
                    columns={variantColumns}
                    pagination={false}
                    size="small"
                    rowKey={(record, index) => record.id || index}
                    scroll={{ x: 1000 }}
                    bordered
                    style={{
                      background: 'white',
                      borderRadius: 6
                    }}
                  />
                </div>
              )}
            </Card>

            {/* Unit Conversions */}
            <InlineUnitConversionManager
              enabled={enableUnitConversion}
              onEnabledChange={setEnableUnitConversion}
              conversions={inlineUnitConversions}
              onConversionsChange={setInlineUnitConversions}
              variants={variants}
            />
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Product Settings */}
            <Card title="Thông tin bổ sung" style={{ marginBottom: 24 }}>
              <Form.Item
                name="loai_san_pham_id"
                label="Loại sản phẩm"
              >
                <Select placeholder="Chọn loại sản phẩm" allowClear>
                  {Array.isArray(categories) && categories.map(cat => (
                    <Option key={cat?.id} value={cat?.id}>{cat?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="nhan_hieu_id"
                label="Nhãn hiệu"
              >
                <Select placeholder="Chọn nhãn hiệu" allowClear>
                  {Array.isArray(brands) && brands.map(brand => (
                    <Option key={brand?.id} value={brand?.id}>{brand?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="tag_id"
                label="Tag"
              >
                <Select placeholder="Chọn tag" allowClear>
                  {Array.isArray(tags) && tags.map(tag => (
                    <Option key={tag?.id} value={tag?.id}>{tag?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="trang_thai"
                label="Trạng thái"
              >
                <Select placeholder="Chọn trạng thái">
                  <Option value="active">Hoạt động</Option>
                  <Option value="inactive">Không hoạt động</Option>
                </Select>
              </Form.Item>
            </Card>

            {/* Product Info */}
            <Card title="Thông tin hiện tại" size="small">
              <div style={{ color: '#666', fontSize: '14px' }}>
                <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
                  <p style={{ margin: '4px 0' }}><strong>ID:</strong> {product.id}</p>
                  <p style={{ margin: '4px 0' }}><strong>Số phiên bản:</strong> {product.phienBanList?.length || 0}</p>
                  <p style={{ margin: '4px 0' }}><strong>Số ảnh:</strong> {product.anhList?.length || 0}</p>
                  <p style={{ margin: '4px 0' }}><strong>Ngày tạo:</strong> {product.ngay_tao ? new Date(product.ngay_tao).toLocaleDateString('vi-VN') : '-'}</p>
                  <p style={{ margin: '4px 0' }}><strong>Người tạo:</strong> {product.nguoi_tao || '-'}</p>
                </div>

                <p><strong>Lưu ý:</strong></p>
                <ul style={{ paddingLeft: 16, margin: 0 }}>
                  <li>Có thể chỉnh sửa tất cả thông tin sản phẩm</li>
                  <li>Thêm/xóa/sửa phiên bản trực tiếp</li>
                  <li>Cập nhật ảnh sản phẩm</li>
                  <li>Thay đổi trạng thái hoạt động</li>
                </ul>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* Variant Modal */}
      <VariantModal
        visible={showVariantModal}
        onCancel={() => setShowVariantModal(false)}
        onSave={saveVariant}
        editingVariant={editingVariant}
        attributes={[...attributes, ...productAttributes]}
      />

      {/* Attribute Modal */}
      <AttributeModal
        visible={showAttributeModal}
        onCancel={() => setShowAttributeModal(false)}
        onSave={saveAttribute}
        editingAttribute={editingAttribute}
      />

      {/* Unit Conversion Modal */}
      <UnitConversionModal
        visible={showUnitModal}
        onCancel={() => setShowUnitModal(false)}
        onSave={saveUnitConversion}
        editingConversion={editingUnit}
      />

      {/* Image Selector Modal */}
      <ImageSelector
        visible={showImageSelector}
        onCancel={() => {
          setShowImageSelector(false);
          setSelectedVariantIndex(null);
        }}
        onSelect={handleImageSelect}
        images={productImages}
        selectedImage={
          selectedVariantIndex !== null && variants[selectedVariantIndex]?.anh_phien_ban?.[0]
            ? variants[selectedVariantIndex].anh_phien_ban[0]
            : null
        }
        title="Chọn ảnh đại diện cho phiên bản"
      />
    </div>
  );
};

export default EditProduct;
