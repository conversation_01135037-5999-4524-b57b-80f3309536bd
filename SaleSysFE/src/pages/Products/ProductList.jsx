import React, { useState, useEffect } from 'react';
import { message, Image, Tag, Space, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined
} from '@ant-design/icons';

// Import common components
import { DataTable, SimpleSearchFilter, PageHeader } from '../../components/Common';
import { usePermissions } from '../../contexts/PermissionContext';
import { useProducts, useCategories, useBrands, useDeleteProduct } from '../../hooks/useProducts';

const { Text } = Typography;

const ProductList = () => {
  const navigate = useNavigate();
  const { canCreate, canEdit, canDelete, canView } = usePermissions();

  // State
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({});

  // API hooks
  const {
    data: productsResponse,
    isLoading: loading,
    error: productsError
  } = useProducts({
    page: pagination.current,
    limit: pagination.pageSize,
    ...filters
  });

  const { data: categories = [] } = useCategories();
  const { data: brands = [] } = useBrands();
  const deleteProductMutation = useDeleteProduct();

  // Extract products array from response
  console.log('🛍️ ProductList - productsResponse:', productsResponse);
  const products = Array.isArray(productsResponse) ? productsResponse : (productsResponse?.data || []);
  console.log('🛍️ ProductList - products:', products);

  // Update pagination when products data changes
  useEffect(() => {
    if (productsResponse && productsResponse.pagination) {
      setPagination(prev => ({
        ...prev,
        total: productsResponse.pagination.total
      }));
    }
  }, [productsResponse]);

  // Table columns
  const columns = [
    {
      title: 'Hình ảnh',
      dataIndex: 'anh_dai_dien',
      key: 'anh_dai_dien',
      width: 80,
      render: (anhDaiDien) => {
        if (!anhDaiDien) {
          return (
            <div
              style={{
                width: 50,
                height: 50,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: 12
              }}
            >
              Không có ảnh
            </div>
          );
        }

        return (
          <Image
            src={anhDaiDien.thumbnail_url || anhDaiDien.url}
            alt="Product"
            width={50}
            height={50}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            preview={{
              src: anhDaiDien.optimized_url || anhDaiDien.url
            }}
          />
        );
      }
    },
    {
      title: 'Nhãn hiệu',
      dataIndex: 'nhan_hieu',
      key: 'nhan_hieu',
      width: 120,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'ten',
      key: 'ten',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.loai_san_pham} • {record.nhan_hieu}
          </Text>
        </div>
      )
    },
    {
      title: 'Loại sản phẩm',
      dataIndex: 'loai_san_pham',
      key: 'loai_san_pham',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          {/* <Text type="secondary" style={{ fontSize: 12 }}>
            {record.loai_san_pham} • {record.nhan_hieu}
          </Text> */}
        </div>
      )
    },
    {
      title: 'Số phiên bản',
      dataIndex: 'so_phien_ban',
      key: 'so_phien_ban',
      width: 120,
      render: (count) => (
        <Tag color="blue">
          {count || 0} phiên bản
        </Tag>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 120,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? 'Hoạt động' : 'Ngừng bán'}
        </Tag>
      )
    }
  ];



  // Event handlers
  const handleSearch = (searchValue) => {
    setFilters(prev => ({ ...prev, search: searchValue }));
    setPagination(prev => ({ ...prev, current: 1 })); // Reset to first page
  };



  const handlePageChange = (page, pageSize) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize
    }));
  };

  const handleCreate = () => {
    navigate('/products/create');
  };

  const handleEdit = (record) => {
    navigate(`/products/edit/${record.id}`);
  };

  const handleView = (record) => {
    navigate(`/products/detail/${record.id}`);
  };

  const handleDelete = async (record) => {
    try {
      await deleteProductMutation.mutateAsync(record.id);
      message.success(`Đã xóa sản phẩm ${record.ten}`);
    } catch (error) {
      message.error('Không thể xóa sản phẩm');
    }
  };



  // Page header actions
  const actions = [];
  
  if (canCreate('SAN_PHAM')) {
    actions.push({
      type: 'primary',
      icon: <PlusOutlined />,
      label: 'Thêm sản phẩm',
      onClick: handleCreate
    });
  }

  return (
    <div>
      <PageHeader
        title="Danh sách sản phẩm"
        subTitle="Quản lý thông tin sản phẩm"
        actions={actions}
        statistics={[
          {
            title: 'Tổng sản phẩm',
            value: products.length,
            valueStyle: { color: '#1890ff' }
          },
          {
            title: 'Đang bán',
            value: products.filter(p => p.trang_thai === 'active').length,
            valueStyle: { color: '#52c41a' }
          },
          {
            title: 'Có ảnh',
            value: products.filter(p => p.anh_dai_dien).length,
            valueStyle: { color: '#52c41a' }
          },
          {
            title: 'Chưa có ảnh',
            value: products.filter(p => !p.anh_dai_dien).length,
            valueStyle: { color: '#fa8c16' }
          }
        ]}
      />

      <DataTable
        title="Tất cả sản phẩm"
        data={products}
        columns={columns}
        loading={loading}

        // Search and filter
        showSearch={true}
        searchPlaceholder="Tìm kiếm sản phẩm theo tên, mã..."
        onSearch={handleSearch}
        filters={[
          {
            key: 'category_id',
            placeholder: 'Loại sản phẩm',
            options: Array.isArray(categories) ? categories.map(cat => ({
              value: cat.id,
              label: cat.ten
            })) : []
          },
          {
            key: 'brand_id',
            placeholder: 'Nhãn hiệu',
            options: Array.isArray(brands) ? brands.map(brand => ({
              value: brand.id,
              label: brand.ten
            })) : []
          },
          {
            key: 'status',
            placeholder: 'Trạng thái',
            options: [
              { value: 'active', label: 'Hoạt động' },
              { value: 'inactive', label: 'Ngừng bán' }
            ]
          }
        ]}
        onFilter={(key, value) => {
          setFilters(prev => ({ ...prev, [key]: value }));
        }}

        // Advanced filter
        showAdvancedFilter={true}
        advancedFilters={[
          // Thông tin cơ bản
          {
            key: 'category_id',
            label: 'Loại sản phẩm',
            type: 'select',
            category: 'Thông tin cơ bản',
            placeholder: 'Chọn loại sản phẩm',
            options: Array.isArray(categories) ? categories.map(cat => ({
              value: cat.id,
              label: cat.ten
            })) : []
          },
          {
            key: 'brand_id',
            label: 'Nhãn hiệu',
            type: 'multiSelect',
            category: 'Thông tin cơ bản',
            placeholder: 'Chọn nhãn hiệu',
            options: Array.isArray(brands) ? brands.map(brand => ({
              value: brand.id,
              label: brand.ten
            })) : []
          },
          {
            key: 'ngay_tao',
            label: 'Ngày tạo sản phẩm',
            type: 'dateRange',
            category: 'Thông tin cơ bản'
          },

          // Giá và tồn kho
          {
            key: 'gia_ban',
            label: 'Khoảng giá bán',
            type: 'numberRange',
            category: 'Giá và tồn kho',
            min: 0,
            max: 10000000
          },
          {
            key: 'ton_kho',
            label: 'Số lượng tồn kho',
            type: 'numberRange',
            category: 'Giá và tồn kho',
            min: 0
          },
          {
            key: 'trang_thai_ton_kho',
            label: 'Trạng thái tồn kho',
            type: 'checkbox',
            category: 'Giá và tồn kho',
            options: [
              { value: 'con_hang', label: 'Còn hàng' },
              { value: 'het_hang', label: 'Hết hàng' },
              { value: 'sap_het', label: 'Sắp hết hàng' }
            ]
          },

          // Trạng thái và phân loại
          {
            key: 'trang_thai_san_pham',
            label: 'Trạng thái sản phẩm',
            type: 'radio',
            category: 'Trạng thái',
            options: [
              { value: 'tat_ca', label: 'Tất cả' },
              { value: 'hoat_dong', label: 'Đang hoạt động' },
              { value: 'ngung_ban', label: 'Ngừng bán' },
              { value: 'cho_duyet', label: 'Chờ duyệt' }
            ]
          },
          {
            key: 'ap_dung_thuat',
            label: 'Áp dụng thuế',
            type: 'select',
            category: 'Trạng thái',
            options: [
              { value: 'co', label: 'Có áp dụng' },
              { value: 'khong', label: 'Không áp dụng' }
            ]
          }
        ]}
        onAdvancedFilter={(values) => {
          console.log('Advanced filter values:', values);
          setFilters(prev => ({ ...prev, ...values }));
        }}
        onResetAdvancedFilter={() => {
          setFilters({});
        }}

        // Add button
        showAddButton={canCreate('SAN_PHAM')}
        onAdd={handleCreate}
        addButtonText="Thêm sản phẩm"

        // Pagination
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} của ${total} sản phẩm`
        }}
        onPageChange={handlePageChange}

        // Actions
        onEdit={canEdit('SAN_PHAM') ? handleEdit : undefined}
        onDelete={canDelete('SAN_PHAM') ? handleDelete : undefined}
        onView={canView('SAN_PHAM') ? handleView : undefined}

        rowKey="id"
        style={{ marginTop: 16 }}
      />
    </div>
  );
};

export default ProductList;
