import React, { useState } from 'react';
import { Tag, Modal, Form, Input } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

import { DataTable, SearchFilter, PageHeader } from '../../components/Common';
import { usePermissions } from '../../contexts/PermissionContext';
import { useCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } from '../../hooks/useProducts';

const Categories = () => {
  const { canCreate, canEdit, canDelete } = usePermissions();

  // State
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();

  // API hooks
  const { data: categories = [], isLoading: loading, error } = useCategories();
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const deleteCategoryMutation = useDeleteCategory();



  // Table columns
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (text) => <Tag color="blue">#{text}</Tag>
    },
    {
      title: 'Tên loại sản phẩm',
      dataIndex: 'ten',
      key: 'ten'
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta',
      render: (text) => text || <span style={{ color: '#999', fontStyle: 'italic' }}>Chưa có mô tả</span>
    },
    {
      title: 'Người tạo',
      dataIndex: 'nguoi_tao',
      key: 'nguoi_tao',
      width: 120
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'ngay_tao',
      key: 'ngay_tao',
      width: 150,
      render: (date) => new Date(date).toLocaleDateString('vi-VN')
    }
  ];

  // Event handlers
  const handleCreate = () => {
    setEditingCategory(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingCategory(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = async (record) => {
    try {
      await deleteCategoryMutation.mutateAsync(record.id);
    } catch (error) {
      // Error message handled by mutation
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingCategory) {
        await updateCategoryMutation.mutateAsync({
          id: editingCategory.id,
          data: values
        });
      } else {
        await createCategoryMutation.mutateAsync(values);
      }

      setIsModalVisible(false);
      form.resetFields();
      setEditingCategory(null);
    } catch (error) {
      // Error message handled by mutation
    }
  };

  const actions = [];
  if (canCreate('LOAI_SAN_PHAM')) {
    actions.push({
      type: 'primary',
      icon: <PlusOutlined />,
      label: 'Thêm loại sản phẩm',
      onClick: handleCreate
    });
  }

  return (
    <div>
      <PageHeader
        title="Loại sản phẩm"
        subTitle="Quản lý phân loại sản phẩm"
        actions={actions}
      />

      <DataTable
        data={categories}
        columns={columns}
        loading={loading}
        onEdit={canEdit('LOAI_SAN_PHAM') ? handleEdit : undefined}
        onDelete={canDelete('LOAI_SAN_PHAM') ? handleDelete : undefined}
        rowKey="id"
      />

      <Modal
        title={editingCategory ? 'Chỉnh sửa loại sản phẩm' : 'Thêm loại sản phẩm'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={createCategoryMutation.isLoading || updateCategoryMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="ten"
            label="Tên loại sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập tên loại sản phẩm' }]}
          >
            <Input placeholder="Nhập tên loại sản phẩm" />
          </Form.Item>

          <Form.Item
            name="mo_ta"
            label="Mô tả"
          >
            <Input.TextArea
              placeholder="Nhập mô tả loại sản phẩm"
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Categories;
