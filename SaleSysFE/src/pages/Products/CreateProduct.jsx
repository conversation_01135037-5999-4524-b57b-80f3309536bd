import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  Upload,
  Switch,
  InputNumber,
  Space,
  Divider,
  Typography,
  message,
  Spin,
  Table,
  Modal,
  Tag,
  Image,
  Avatar
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  CameraOutlined
} from '@ant-design/icons';
import ImageUpload from '../../components/ImageUpload/ImageUpload';
import ImageSelector from '../../components/ImageSelector/ImageSelector';
import SimpleUploadTest from '../../components/SimpleUploadTest';
import DebugUpload from '../../components/DebugUpload';
import TestImageUpload from '../../components/TestImageUpload';
import BasicUploadTest from '../../components/BasicUploadTest';
import TestNavigation from '../../components/TestNavigation';
import { useNavigate } from 'react-router-dom';
import { productsAPI } from '../../services/api';
import VariantModal from '../../components/Products/VariantModal';
import AttributeModal from '../../components/Products/AttributeModal';
import UnitConversionModal from '../../components/Products/UnitConversionModal';
import InlineAttributeManager from '../../components/Products/InlineAttributeManager';
import InlineWarehouseManager from '../../components/Products/InlineWarehouseManager';
import InlineUnitConversionManager from '../../components/Products/InlineUnitConversionManager';
import {
  useCategories,
  useBrands,
  useTags,
  useAttributes,
  useCreateProduct
} from '../../hooks/useProducts';
import { useWarehouses } from '../../hooks/useWarehouses';
import styles from './CreateProduct.module.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateProduct = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  // API hooks
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const { data: brands = [], isLoading: brandsLoading, error: brandsError } = useBrands();
  const { data: tags = [], isLoading: tagsLoading, error: tagsError } = useTags();
  const { data: attributes = [], isLoading: attributesLoading, error: attributesError } = useAttributes();
  const { data: warehouses = [], isLoading: warehousesLoading } = useWarehouses();
  const createProductMutation = useCreateProduct();

  // Debug logs
  console.log("📊 CreateProduct Data Debug:", {
    categories: { data: categories, loading: categoriesLoading, error: categoriesError },
    brands: { data: brands, loading: brandsLoading, error: brandsError },
    tags: { data: tags, loading: tagsLoading, error: tagsError },
    attributes: { data: attributes, loading: attributesLoading, error: attributesError }
  });
  // State for form data
  const [productData, setProductData] = useState({
    ten: '',
    loai_san_pham_id: null,
    nhan_hieu_id: null,
    tag_id: null,
    mo_ta: '',
    thuoc_tinh: [],
    phien_ban: [],
    anh_san_pham: []
  });

  // State for variants
  const [variants, setVariants] = useState([]);
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [editingVariant, setEditingVariant] = useState(null);

  // State for attributes
  const [productAttributes, setProductAttributes] = useState([]);
  const [showAttributeModal, setShowAttributeModal] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState(null);

  // State for unit conversions
  const [unitConversions, setUnitConversions] = useState([]);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [editingUnit, setEditingUnit] = useState(null);

  // State for inventory
  const [enableInventory, setEnableInventory] = useState(false);
  const [inventoryData, setInventoryData] = useState({});

  // State for unit conversions (inline)
  const [enableUnitConversion, setEnableUnitConversion] = useState(false);
  const [inlineUnitConversions, setInlineUnitConversions] = useState([]);

  // State for images
  const [productImages, setProductImages] = useState([]);
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [selectedVariantIndex, setSelectedVariantIndex] = useState(null);

  // Check if any data is loading
  const isLoading = categoriesLoading || brandsLoading || tagsLoading || attributesLoading || warehousesLoading;

  const handleSubmit = async (values) => {
    try {
      const formData = {
        ...values,
        phien_ban: variants,
        anh_san_pham: productImages.map(img => ({
          url: img.url,
          public_id: img.public_id,
          thumbnail_url: img.thumbnail_url,
          optimized_url: img.optimized_url
        })),
        inventory_data: enableInventory ? inventoryData : null,
        unit_conversions: enableUnitConversion ? inlineUnitConversions : null
      };

      await createProductMutation.mutateAsync(formData);
      navigate('/products');
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Handle product images upload
  const handleProductImagesChange = (imageList) => {
    console.log('🖼️ handleProductImagesChange called with:', imageList);
    setProductImages(imageList);

    // Auto-assign first image to variants that don't have images
    if (imageList.length > 0 && variants.length > 0) {
      console.log('🔄 Auto-assigning first image to variants...');
      const newVariants = variants.map(variant => {
        if (!variant.anh_phien_ban || variant.anh_phien_ban.length === 0) {
          return {
            ...variant,
            anh_phien_ban: [imageList[0]] // Assign first image as default
          };
        }
        return variant;
      });
      setVariants(newVariants);
      console.log('✅ Variants updated with images');
    }
  };

  // Handle variant image selection
  const handleVariantImageClick = (variantIndex) => {
    if (productImages.length === 0) {
      message.warning('Vui lòng thêm ảnh sản phẩm trước');
      return;
    }
    setSelectedVariantIndex(variantIndex);
    setShowImageSelector(true);
  };

  // Handle image selection for variant
  const handleImageSelect = (selectedImage) => {
    if (selectedVariantIndex !== null) {
      const newVariants = [...variants];
      newVariants[selectedVariantIndex].anh_phien_ban = [selectedImage];
      setVariants(newVariants);
    }
    setShowImageSelector(false);
    setSelectedVariantIndex(null);
  };

  const addVariant = () => {
    setEditingVariant(null);
    setShowVariantModal(true);
  };

  const editVariant = (variant, index) => {
    setEditingVariant({ ...variant, index });
    setShowVariantModal(true);
  };

  const deleteVariant = (index) => {
    const newVariants = variants.filter((_, i) => i !== index);
    setVariants(newVariants);
  };

  const saveVariant = (variantData, editIndex) => {
    if (editIndex !== undefined && editIndex !== null) {
      // Edit existing variant
      const newVariants = [...variants];
      newVariants[editIndex] = variantData;
      setVariants(newVariants);
    } else {
      // Add new variant - auto-assign first image if no image selected
      const newVariant = {
        ...variantData,
        anh_phien_ban: variantData.anh_phien_ban && variantData.anh_phien_ban.length > 0
          ? variantData.anh_phien_ban
          : (productImages.length > 0 ? [productImages[0]] : [])
      };
      setVariants([...variants, newVariant]);
    }
    setShowVariantModal(false);
  };

  // Attribute management functions
  const addAttribute = () => {
    setEditingAttribute(null);
    setShowAttributeModal(true);
  };

  const editAttribute = (attribute, index) => {
    setEditingAttribute({ ...attribute, index });
    setShowAttributeModal(true);
  };

  const deleteAttribute = (index) => {
    const newAttributes = productAttributes.filter((_, i) => i !== index);
    setProductAttributes(newAttributes);

    // Regenerate variants when attribute is deleted
    generateVariantsFromAttributes(newAttributes);
  };

  const saveAttribute = (attributeData, editIndex) => {
    let newAttributes;
    if (editIndex !== undefined && editIndex !== null) {
      // Edit existing attribute
      newAttributes = [...productAttributes];
      newAttributes[editIndex] = attributeData;
    } else {
      // Add new attribute
      newAttributes = [...productAttributes, attributeData];
    }

    setProductAttributes(newAttributes);
    setShowAttributeModal(false);

    // Auto-generate variants based on attributes
    generateVariantsFromAttributes(newAttributes);
  };

  // Function to generate all possible combinations of attributes
  const generateVariantsFromAttributes = (attributes) => {
    if (attributes.length === 0) {
      setVariants([]);
      return;
    }

    // Get all attribute values
    const attributeValues = attributes.map(attr => ({
      name: attr.ten,
      values: attr.gia_tri
    }));

    // Generate all combinations
    const combinations = generateCombinations(attributeValues);

    // Create variants from combinations
    const newVariants = combinations.map((combination, index) => {
      const variantName = combination.map(c => c.value).join(' - ');
      const productName = form.getFieldValue('ten') || 'sản phẩm';

      // Get common prices from form
      const commonRetailPrice = form.getFieldValue('gia_ban_le_chung') || 0;
      const commonWholesalePrice = form.getFieldValue('gia_ban_buon_chung') || 0;
      const commonImportPrice = form.getFieldValue('gia_nhap_chung') || 0;

      return {
        ten_phien_ban: `${productName} - ${variantName}`,
        ma: '', // Will be auto-generated
        ma_vach: '',
        mo_ta: '',
        khoi_luong: 0,
        don_vi_tinh: 'cái',
        gia_le: commonRetailPrice,
        gia_buon: commonWholesalePrice,
        gia_nhap: commonImportPrice,
        thuoc_tinh: combination.map(c => ({
          ten: c.attribute,
          gia_tri: c.value
        })),
        anh_phien_ban: productImages.length > 0 ? [productImages[0]] : [] // Auto-assign first image
      };
    });

    setVariants(newVariants);
  };

  // Helper function to generate all combinations
  const generateCombinations = (attributeValues) => {
    if (attributeValues.length === 0) return [];
    if (attributeValues.length === 1) {
      return attributeValues[0].values.map(value => [{
        attribute: attributeValues[0].name,
        value: value
      }]);
    }

    const result = [];
    const firstAttribute = attributeValues[0];
    const restCombinations = generateCombinations(attributeValues.slice(1));

    firstAttribute.values.forEach(value => {
      restCombinations.forEach(combination => {
        result.push([{
          attribute: firstAttribute.name,
          value: value
        }, ...combination]);
      });
    });

    return result;
  };

  // Unit conversion management functions
  const addUnitConversion = () => {
    setEditingUnit(null);
    setShowUnitModal(true);
  };

  const editUnitConversion = (unit, index) => {
    setEditingUnit({ ...unit, index });
    setShowUnitModal(true);
  };

  const deleteUnitConversion = (index) => {
    const newUnits = unitConversions.filter((_, i) => i !== index);
    setUnitConversions(newUnits);
  };

  const saveUnitConversion = (unitData, editIndex) => {
    if (editIndex !== undefined && editIndex !== null) {
      // Edit existing unit conversion
      const newUnits = [...unitConversions];
      newUnits[editIndex] = unitData;
      setUnitConversions(newUnits);
    } else {
      // Add new unit conversion
      setUnitConversions([...unitConversions, unitData]);
    }
    setShowUnitModal(false);
  };

  const variantColumns = [
    {
      title: '',
      dataIndex: 'selected',
      key: 'selected',
      width: 50,
      render: (_, record, index) => (
        <input
          type="checkbox"
          defaultChecked={true}
          style={{ transform: 'scale(1.2)' }}
        />
      )
    },
    {
      title: '',
      dataIndex: 'anh_phien_ban',
      key: 'anh_phien_ban',
      width: 60,
      render: (images, record, index) => (
        <div
          style={{
            width: 40,
            height: 40,
            background: '#f5f5f5',
            border: '1px dashed #d9d9d9',
            borderRadius: 4,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            overflow: 'hidden'
          }}
          onClick={() => handleVariantImageClick(index)}
          title="Click để chọn ảnh"
        >
          {images && images.length > 0 ? (
            <Image
              src={images[0].thumbnail_url || images[0].url}
              alt="Variant"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
              preview={false}
            />
          ) : (
            <CameraOutlined style={{ fontSize: 16, color: '#bfbfbf' }} />
          )}
        </div>
      )
    },
    {
      title: 'Tên phiên bản',
      dataIndex: 'ten_phien_ban',
      key: 'ten_phien_ban',
      width: 200,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => {
            const newVariants = [...variants];
            newVariants[index].ten_phien_ban = e.target.value;
            setVariants(newVariants);
          }}
          placeholder="Tên phiên bản"
          size="small"
        />
      )
    },
    {
      title: 'Mã SKU',
      dataIndex: 'ma',
      key: 'ma',
      width: 120,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => {
            const newVariants = [...variants];
            newVariants[index].ma = e.target.value;
            setVariants(newVariants);
          }}
          placeholder="Mã SKU"
          size="small"
        />
      )
    },
    {
      title: 'Barcode',
      dataIndex: 'ma_vach',
      key: 'ma_vach',
      width: 120,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => {
            const newVariants = [...variants];
            newVariants[index].ma_vach = e.target.value;
            setVariants(newVariants);
          }}
          placeholder="Barcode"
          size="small"
        />
      )
    },
    {
      title: (
        <div style={{ textAlign: 'center' }}>
          <div>Đơn vị tính</div>
        </div>
      ),
      dataIndex: 'don_vi_tinh',
      key: 'don_vi_tinh',
      width: 100,
      render: (value, record, index) => (
        <Select
          value={value}
          onChange={(val) => {
            const newVariants = [...variants];
            newVariants[index].don_vi_tinh = val;
            setVariants(newVariants);
          }}
          placeholder="Đơn vị"
          size="small"
          style={{ width: '100%' }}
        >
          <Option value="cái">Cái</Option>
          <Option value="chiếc">Chiếc</Option>
          <Option value="kg">Kg</Option>
          <Option value="g">Gram</Option>
          <Option value="lít">Lít</Option>
          <Option value="ml">ML</Option>
          <Option value="m">Mét</Option>
          <Option value="cm">CM</Option>
          <Option value="hộp">Hộp</Option>
          <Option value="thùng">Thùng</Option>
          <Option value="bộ">Bộ</Option>
          <Option value="đôi">Đôi</Option>
        </Select>
      )
    },
    {
      title: (
        <div style={{ textAlign: 'center' }}>
          <div>Giá bán lẻ</div>
          <div style={{ fontSize: '11px', color: '#999', fontWeight: 'normal' }}>(VNĐ)</div>
        </div>
      ),
      dataIndex: 'gia_le',
      key: 'gia_le',
      width: 140,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => {
            const newVariants = [...variants];
            newVariants[index].gia_le = val || 0;
            setVariants(newVariants);
          }}
          placeholder="0"
          size="small"
          style={{ width: '100%' }}
          min={0}
          addonAfter="₫"
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\₫\s?|(,*)/g, '')}
        />
      )
    },
    {
      title: (
        <div style={{ textAlign: 'center' }}>
          <div>Giá bán buôn</div>
          <div style={{ fontSize: '11px', color: '#999', fontWeight: 'normal' }}>(VNĐ)</div>
        </div>
      ),
      dataIndex: 'gia_buon',
      key: 'gia_buon',
      width: 140,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => {
            const newVariants = [...variants];
            newVariants[index].gia_buon = val || 0;
            setVariants(newVariants);
          }}
          placeholder="0"
          size="small"
          style={{ width: '100%' }}
          min={0}
          addonAfter="₫"
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\₫\s?|(,*)/g, '')}
        />
      )
    },
    {
      title: (
        <div style={{ textAlign: 'center' }}>
          <div>Giá nhập</div>
          <div style={{ fontSize: '11px', color: '#999', fontWeight: 'normal' }}>(VNĐ)</div>
        </div>
      ),
      dataIndex: 'gia_nhap',
      key: 'gia_nhap',
      width: 140,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => {
            const newVariants = [...variants];
            newVariants[index].gia_nhap = val || 0;
            setVariants(newVariants);
          }}
          placeholder="0"
          size="small"
          style={{ width: '100%' }}
          min={0}
          addonAfter="₫"
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\₫\s?|(,*)/g, '')}
        />
      )
    }
  ];

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className={styles.createProductContainer}>
      {/* Test Navigation */}
      <div style={{ marginBottom: 24 }}>
        <TestNavigation />
      </div>

      {/* Header */}
      <div className={styles.header}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/products')}
          className={styles.backButton}
        >
          Quay lại danh sách sản phẩm
        </Button>

        <div className={styles.headerContent}>
          <Title level={2} className={styles.title}>
            Tạo sản phẩm mới
          </Title>

          <div className={styles.actionButtons}>
            <Button onClick={() => navigate('/products')}>
              Thoát
            </Button>
            <Button onClick={() => form.submit()}>
              Lưu và tạo mới tiếp
            </Button>
            <Button
              type="primary"
              loading={createProductMutation.isLoading}
              onClick={() => form.submit()}
            >
              Lưu
            </Button>
          </div>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ...productData,
          gia_ban_le_chung: 0,
          gia_ban_buon_chung: 0,
          gia_nhap_chung: 0
        }}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Basic Information */}
            <Card title="Thông tin chung" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="ten"
                    label="Tên sản phẩm"
                    rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
                  >
                    <Input
                      placeholder="Nhập tên sản phẩm"
                      onChange={(e) => {
                        // Update variant names when product name changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            ten_phien_ban: variant.ten_phien_ban.replace(
                              /^[^-]*/,
                              e.target.value || 'sản phẩm'
                            )
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="loai_san_pham_id"
                    label="Loại sản phẩm"
                  >
                    <Select placeholder="Chọn loại sản phẩm" allowClear>
                      {categories.map(cat => (
                        <Option key={cat.id} value={cat.id}>{cat.ten}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="mo_ta"
                label="Mô tả sản phẩm"
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập mô tả sản phẩm"
                  showCount
                  maxLength={1000}
                />
              </Form.Item>

              {/* Giá sản phẩm chung */}
              

              <Card title="Giá sản phẩm">
                <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="gia_ban_le_chung"
                    label={
                      <span>
                         Giá bán lẻ
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                    tooltip="Giá này sẽ được áp dụng mặc định cho tất cả phiên bản mới"
                  >
                    <InputNumber
                      placeholder="0"
                      style={{ width: '100%' }}
                      min={0}
                      addonAfter="₫"
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\₫\s?|(,*)/g, '')}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_le: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="gia_ban_buon_chung"
                    label={
                      <span>
                        Giá bán buôn
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                    tooltip="Giá này sẽ được áp dụng mặc định cho tất cả phiên bản mới"
                  >
                    <InputNumber
                      placeholder="0"
                      style={{ width: '100%' }}
                      min={0}
                      addonAfter="₫"
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\₫\s?|(,*)/g, '')}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_buon: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="gia_nhap_chung"
                    label={
                      <span>
                        Giá nhập
                        <span style={{ fontSize: '12px', color: '#999', marginLeft: 8 }}>
                          (VNĐ)
                        </span>
                      </span>
                    }
                    tooltip="Giá này sẽ được áp dụng mặc định cho tất cả phiên bản mới"
                  >
                    <InputNumber
                      placeholder="0"
                      style={{ width: '100%' }}
                      min={0}
                      addonAfter="₫"
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\₫\s?|(,*)/g, '')}
                      onChange={(value) => {
                        // Update existing variants when common price changes
                        if (variants.length > 0) {
                          const newVariants = variants.map(variant => ({
                            ...variant,
                            gia_nhap: value || 0
                          }));
                          setVariants(newVariants);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              </Card>

              <div style={{
                background: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: 6,
                padding: 12,
                marginTop: 16,
                fontSize: '13px',
                color: '#52c41a'
              }}>
                <span style={{ fontWeight: 'bold' }}>💡 Lưu ý:</span> Giá này sẽ được áp dụng mặc định cho tất cả phiên bản mới.
                Bạn có thể chỉnh sửa giá riêng cho từng phiên bản trong bảng bên dưới.
              </div>
            </Card>

            {/* Product Images */}
            <Card title="Ảnh sản phẩm" style={{ marginBottom: 24 }}>
              {/* Test Upload Components */}
              {/* <div style={{ marginBottom: 20 }}>
                <BasicUploadTest />
              </div> */}

              <ImageUpload
                value={productImages}
                onChange={handleProductImagesChange}
                maxCount={10}
                folder="products"
                width={800}
                height={600}
              />
              <div style={{
                marginTop: 12,
                fontSize: '13px',
                color: '#666',
                background: '#f9f9f9',
                padding: 8,
                borderRadius: 4
              }}>
                💡 <strong>Lưu ý:</strong> Ảnh đầu tiên sẽ được sử dụng làm ảnh đại diện mặc định cho các phiên bản sản phẩm.
                Bạn có thể click vào ảnh đại diện của từng phiên bản để chọn ảnh khác.
              </div>
            </Card>

            

            {/* Inventory Settings - Moved up before Attributes */}
            <InlineWarehouseManager
              enabled={enableInventory}
              onEnabledChange={setEnableInventory}
              warehouses={warehouses}
              inventoryData={inventoryData}
              onInventoryChange={setInventoryData}
            />

            {/* Attributes - Inline Style */}
            <InlineAttributeManager
              attributes={productAttributes}
              onAttributesChange={(newAttributes) => {
                setProductAttributes(newAttributes);
                generateVariantsFromAttributes(newAttributes);
              }}
            />
{/* Product Variants */}
            <Card
              title={`Phiên bản (${variants.length})`}
              style={{ marginBottom: 24 }}
              extra={
                variants.length > 0 && (
                  <Space>
                    <Button
                      icon={<PlusOutlined />}
                      onClick={addVariant}
                      size="small"
                    >
                      Thêm phiên bản
                    </Button>
                    <Button
                      type="primary"
                      ghost
                      size="small"
                      onClick={() => {
                        const commonRetailPrice = form.getFieldValue('gia_ban_le_chung') || 0;
                        const commonWholesalePrice = form.getFieldValue('gia_ban_buon_chung') || 0;
                        const commonImportPrice = form.getFieldValue('gia_nhap_chung') || 0;

                        const newVariants = variants.map(variant => ({
                          ...variant,
                          gia_le: commonRetailPrice,
                          gia_buon: commonWholesalePrice,
                          gia_nhap: commonImportPrice
                        }));
                        setVariants(newVariants);
                        message.success('Đã áp dụng giá chung cho tất cả phiên bản!');
                      }}
                      style={{ color: '#52c41a', borderColor: '#52c41a' }}
                    >
                      💰 Áp dụng giá chung
                    </Button>
                  </Space>
                )
              }
            >
              {variants.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                  <Text>
                    {productAttributes.length === 0
                      ? 'Thêm thuộc tính để tự động tạo phiên bản sản phẩm'
                      : 'Đang tạo phiên bản từ thuộc tính...'
                    }
                  </Text>
                </div>
              ) : (
                <div style={{ overflowX: 'auto' }}>
                  <Table
                    dataSource={variants}
                    columns={variantColumns}
                    pagination={false}
                    size="small"
                    rowKey={(record, index) => index}
                    scroll={{ x: 1000 }}
                    bordered
                    style={{
                      background: 'white',
                      borderRadius: 6
                    }}
                  />
                </div>
              )}
            </Card>
            {/* Unit Conversions - Inline Style */}
            <InlineUnitConversionManager
              enabled={enableUnitConversion}
              onEnabledChange={setEnableUnitConversion}
              conversions={inlineUnitConversions}
              onConversionsChange={setInlineUnitConversions}
              variants={variants}
            />
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Product Settings */}
            <Card title="Thông tin bổ sung" style={{ marginBottom: 24 }}>
              <Form.Item
                name="loai_san_pham_id_right"
                label="Loại sản phẩm"
              >
                <Select placeholder="Chọn loại sản phẩm" allowClear>
                  {Array.isArray(categories) && categories.map(cat => (
                    <Option key={cat?.id} value={cat?.id}>{cat?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="nhan_hieu_id"
                label="Nhãn hiệu"
              >
                <Select placeholder="Chọn nhãn hiệu" allowClear>
                  {Array.isArray(brands) && brands.map(brand => (
                    <Option key={brand?.id} value={brand?.id}>{brand?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="tag_id"
                label="Tag"
              >
                <Select placeholder="Chọn tag" allowClear>
                  {Array.isArray(tags) && tags.map(tag => (
                    <Option key={tag?.id} value={tag?.id}>{tag?.ten}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* Variant Modal */}
      <VariantModal
        visible={showVariantModal}
        onCancel={() => setShowVariantModal(false)}
        onSave={saveVariant}
        editingVariant={editingVariant}
        attributes={[...attributes, ...productAttributes]}
      />

      {/* Attribute Modal */}
      <AttributeModal
        visible={showAttributeModal}
        onCancel={() => setShowAttributeModal(false)}
        onSave={saveAttribute}
        editingAttribute={editingAttribute}
      />

      {/* Unit Conversion Modal */}
      <UnitConversionModal
        visible={showUnitModal}
        onCancel={() => setShowUnitModal(false)}
        onSave={saveUnitConversion}
        editingConversion={editingUnit}
      />

      {/* Image Selector Modal */}
      <ImageSelector
        visible={showImageSelector}
        onCancel={() => {
          setShowImageSelector(false);
          setSelectedVariantIndex(null);
        }}
        onSelect={handleImageSelect}
        images={productImages}
        selectedImage={
          selectedVariantIndex !== null && variants[selectedVariantIndex]?.anh_phien_ban?.[0]
            ? variants[selectedVariantIndex].anh_phien_ban[0]
            : null
        }
        title="Chọn ảnh đại diện cho phiên bản"
      />
    </div>
  );
};

export default CreateProduct;
