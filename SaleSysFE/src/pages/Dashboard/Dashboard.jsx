import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Table,
  Tag,
  Progress,
  List,
  Avatar
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';

import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../contexts/PermissionContext';

const { Title, Text } = Typography;

const Dashboard = () => {
  const { user } = useAuth();
  const { canView } = usePermissions();

  // Mock data for charts
  const salesData = [
    { name: 'T1', value: 4000 },
    { name: 'T2', value: 3000 },
    { name: 'T3', value: 2000 },
    { name: 'T4', value: 2780 },
    { name: 'T5', value: 1890 },
    { name: 'T6', value: 2390 },
    { name: 'T7', value: 3490 },
  ];

  const categoryData = [
    { name: '<PERSON><PERSON><PERSON><PERSON> tử', value: 400, color: '#0088FE' },
    { name: 'Thời trang', value: 300, color: '#00C49F' },
    { name: 'Gia dụng', value: 300, color: '#FFBB28' },
    { name: 'Khác', value: 200, color: '#FF8042' },
  ];

  // Mock data for recent orders
  const recentOrders = [
    {
      key: '1',
      ma_don_hang: 'SON000001',
      khach_hang: 'Nguyễn Văn A',
      tong_tien: 500000,
      trang_thai: 'cho_xu_ly',
      ngay_tao: '2023-12-01'
    },
    {
      key: '2',
      ma_don_hang: 'SON000002',
      khach_hang: 'Trần Thị B',
      tong_tien: 750000,
      trang_thai: 'da_xac_nhan',
      ngay_tao: '2023-12-01'
    },
    {
      key: '3',
      ma_don_hang: 'SON000003',
      khach_hang: 'Lê Văn C',
      tong_tien: 300000,
      trang_thai: 'da_giao',
      ngay_tao: '2023-11-30'
    }
  ];

  const orderColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'ma_don_hang',
      key: 'ma_don_hang',
    },
    {
      title: 'Khách hàng',
      dataIndex: 'khach_hang',
      key: 'khach_hang',
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'tong_tien',
      key: 'tong_tien',
      render: (value) => `${value.toLocaleString()} đ`,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      render: (status) => {
        const statusMap = {
          cho_xu_ly: { color: 'orange', text: 'Chờ xử lý' },
          da_xac_nhan: { color: 'blue', text: 'Đã xác nhận' },
          da_giao: { color: 'green', text: 'Đã giao' },
        };
        const { color, text } = statusMap[status] || { color: 'default', text: status };
        return <Tag color={color}>{text}</Tag>;
      },
    },
  ];

  // Mock data for top products
  const topProducts = [
    {
      title: 'iPhone 15 Pro Max',
      description: 'Điện thoại thông minh cao cấp',
      avatar: '📱',
      sales: 45
    },
    {
      title: 'MacBook Air M2',
      description: 'Laptop siêu mỏng nhẹ',
      avatar: '💻',
      sales: 32
    },
    {
      title: 'AirPods Pro',
      description: 'Tai nghe không dây',
      avatar: '🎧',
      sales: 28
    },
    {
      title: 'iPad Air',
      description: 'Máy tính bảng đa năng',
      avatar: '📱',
      sales: 21
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Tổng quan</Title>
        <Text type="secondary">
          Chào mừng {user?.ho_ten} quay trở lại! Đây là tổng quan về hoạt động kinh doanh.
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Doanh thu hôm nay"
              value={12225000}
              precision={0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<DollarOutlined />}
              suffix="đ"
            />
            <div style={{ marginTop: 8 }}>
              <ArrowUpOutlined style={{ color: '#3f8600' }} /> 12.5%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Đơn hàng mới"
              value={57}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ShoppingCartOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <ArrowUpOutlined style={{ color: '#3f8600' }} /> 8.2%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Khách hàng mới"
              value={23}
              valueStyle={{ color: '#722ed1' }}
              prefix={<UserOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <ArrowDownOutlined style={{ color: '#cf1322' }} /> 2.1%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với hôm qua
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Sản phẩm bán chạy"
              value={156}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ShoppingOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <ArrowUpOutlined style={{ color: '#3f8600' }} /> 15.3%
              <Text type="secondary" style={{ marginLeft: 8 }}>
                so với tuần trước
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* Sales Chart */}
        <Col xs={24} lg={16}>
          <Card title="Doanh thu 7 ngày qua" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value.toLocaleString()} đ`, 'Doanh thu']} />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  dot={{ fill: '#1890ff' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Category Chart */}
        <Col xs={24} lg={8}>
          <Card title="Phân loại sản phẩm" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Tables Row */}
      <Row gutter={[16, 16]}>
        {/* Recent Orders */}
        {canView('DON_HANG') && (
          <Col xs={24} lg={14}>
            <Card title="Đơn hàng gần đây" extra={<a href="/orders">Xem tất cả</a>}>
              <Table
                dataSource={recentOrders}
                columns={orderColumns}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        )}

        {/* Top Products */}
        {canView('SAN_PHAM') && (
          <Col xs={24} lg={10}>
            <Card title="Sản phẩm bán chạy" extra={<a href="/products">Xem tất cả</a>}>
              <List
                itemLayout="horizontal"
                dataSource={topProducts}
                renderItem={(item, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar>{item.avatar}</Avatar>}
                      title={item.title}
                      description={item.description}
                    />
                    <div>
                      <Text strong>{item.sales} đã bán</Text>
                      <Progress 
                        percent={(item.sales / 50) * 100} 
                        size="small" 
                        showInfo={false}
                        style={{ width: 60, marginTop: 4 }}
                      />
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default Dashboard;
