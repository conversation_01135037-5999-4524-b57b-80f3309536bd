/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

#root {
  min-height: 100vh;
}

/* Ant Design customizations */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.ant-menu-dark {
  background: #001529;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff;
}

.ant-table-thead>tr>th {
  background: #fafafa;
  font-weight: 600;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-btn {
  border-radius: 6px;
}

.ant-input,
.ant-select-selector {
  border-radius: 6px;
}

/* Custom utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }

  .ant-card {
    margin: 8px;
  }
}