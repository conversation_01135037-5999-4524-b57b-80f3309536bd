import axios from 'axios';
import { message } from 'antd';
import Cookies from 'js-cookie';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          Cookies.remove('token');
          if (window.location.pathname !== '/login') {
            message.error('<PERSON>ên đăng nhập đã hết hạn');
            window.location.href = '/login';
          }
          break;
        
        case 403:
          // Forbidden
          message.error(data.message || 'Bạn không có quyền thực hiện hành động này');
          break;
        
        case 404:
          // Not found
          message.error(data.message || 'Không tìm thấy tài nguyên');
          break;
        
        case 422:
          // Validation error
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              message.error(`${err.field}: ${err.message}`);
            });
          } else {
            message.error(data.message || 'Dữ liệu không hợp lệ');
          }
          break;
        
        case 500:
          // Server error
          message.error('Lỗi máy chủ, vui lòng thử lại sau');
          break;
        
        default:
          message.error(data.message || 'Có lỗi xảy ra');
      }
    } else if (error.request) {
      // Network error
      message.error('Không thể kết nối đến máy chủ');
    } else {
      // Other error
      message.error('Có lỗi xảy ra');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  assignRoles: (id, roleIds) => api.post(`/users/${id}/assign-roles`, { vai_tro_ids: roleIds }),
};

// Roles API
export const rolesAPI = {
  getRoles: () => api.get('/roles'),
  getRole: (id) => api.get(`/roles/${id}`),
  createRole: (roleData) => api.post('/roles', roleData),
  updateRole: (id, roleData) => api.put(`/roles/${id}`, roleData),
  deleteRole: (id) => api.delete(`/roles/${id}`),
  assignPermissions: (id, permissionIds) => api.post(`/roles/${id}/assign-permissions`, { quyen_ids: permissionIds }),
};

// Permissions API
export const permissionsAPI = {
  getPermissions: () => api.get('/permissions'),
  getPermission: (id) => api.get(`/permissions/${id}`),
  createPermission: (permissionData) => api.post('/permissions', permissionData),
  updatePermission: (id, permissionData) => api.put(`/permissions/${id}`, permissionData),
  deletePermission: (id) => api.delete(`/permissions/${id}`),
};

// Products API
export const productsAPI = {
  getProducts: (params) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  createProduct: (productData) => api.post('/products', productData),
  updateProduct: (id, productData) => api.put(`/products/${id}`, productData),
  deleteProduct: (id) => api.delete(`/products/${id}`),

  // Product Categories
  getCategories: () => api.get('/products/categories'),
  getCategory: (id) => api.get(`/products/categories/${id}`),
  createCategory: (categoryData) => api.post('/products/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/products/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/products/categories/${id}`),

  // Product Brands
  getBrands: () => api.get('/products/brands'),
  getBrand: (id) => api.get(`/products/brands/${id}`),
  createBrand: (brandData) => api.post('/products/brands', brandData),
  updateBrand: (id, brandData) => api.put(`/products/brands/${id}`, brandData),
  deleteBrand: (id) => api.delete(`/products/brands/${id}`),

  // Product Tags
  getTags: () => api.get('/products/tags'),
  getTag: (id) => api.get(`/products/tags/${id}`),
  createTag: (tagData) => api.post('/products/tags', tagData),
  updateTag: (id, tagData) => api.put(`/products/tags/${id}`, tagData),
  deleteTag: (id) => api.delete(`/products/tags/${id}`),

  // Product Attributes
  getAttributes: () => api.get('/products/attributes'),
  getAttribute: (id) => api.get(`/products/attributes/${id}`),
  createAttribute: (attributeData) => api.post('/products/attributes', attributeData),
  updateAttribute: (id, attributeData) => api.put(`/products/attributes/${id}`, attributeData),
  deleteAttribute: (id) => api.delete(`/products/attributes/${id}`),

  // Product Variants
  getVariants: (productId) => api.get(`/products/${productId}/variants`),
  getVariant: (productId, variantId) => api.get(`/products/${productId}/variants/${variantId}`),
  createVariant: (productId, variantData) => api.post(`/products/${productId}/variants`, variantData),
  updateVariant: (productId, variantId, variantData) => api.put(`/products/${productId}/variants/${variantId}`, variantData),
  deleteVariant: (productId, variantId) => api.delete(`/products/${productId}/variants/${variantId}`),
};

// Orders API
export const ordersAPI = {
  getOrders: (params) => api.get('/orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (orderData) => api.post('/orders', orderData),
  updateOrder: (id, orderData) => api.put(`/orders/${id}`, orderData),
  deleteOrder: (id) => api.delete(`/orders/${id}`),
};

// Warehouses API
export const warehousesAPI = {
  getWarehouses: (params) => api.get('/warehouses', { params }),
  getWarehouse: (id) => api.get(`/warehouses/${id}`),
  createWarehouse: (warehouseData) => api.post('/warehouses', warehouseData),
  updateWarehouse: (id, warehouseData) => api.put(`/warehouses/${id}`, warehouseData),
  deleteWarehouse: (id) => api.delete(`/warehouses/${id}`),

  // Inventory Management
  getInventory: (params) => api.get('/warehouses/inventory', { params }),
  getInventoryByWarehouse: (warehouseId, params) => api.get(`/warehouses/${warehouseId}/inventory`, { params }),
  adjustInventory: (adjustmentData) => api.post('/warehouses/inventory/adjust', adjustmentData),

  // Stock Movements
  getStockMovements: (params) => api.get('/warehouses/movements', { params }),
  getStockMovement: (id) => api.get(`/warehouses/movements/${id}`),
  createStockMovement: (movementData) => api.post('/warehouses/movements', movementData),

  // Stock Checks
  getStockChecks: (params) => api.get('/warehouses/stock-checks', { params }),
  getStockCheck: (id) => api.get(`/warehouses/stock-checks/${id}`),
  createStockCheck: (checkData) => api.post('/warehouses/stock-checks', checkData),
  updateStockCheck: (id, checkData) => api.put(`/warehouses/stock-checks/${id}`, checkData),
  deleteStockCheck: (id) => api.delete(`/warehouses/stock-checks/${id}`),

  // Stock Check Details
  getStockCheckDetails: (id, params) => api.get(`/warehouses/stock-checks/${id}/details`, { params }),
  updateStockCheckItem: (stockCheckId, itemId, data) => api.put(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`, data),
  completeStockCheck: (id) => api.post(`/warehouses/stock-checks/${id}/complete`),

  // Stock Check Products
  getAvailableProducts: (id, params) => api.get(`/warehouses/stock-checks/${id}/available-products`, { params }),
  addProductToStockCheck: (id, data) => api.post(`/warehouses/stock-checks/${id}/products`, data),
  removeProductFromStockCheck: (stockCheckId, itemId) => api.delete(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`),
};

//Customer API 
export const customerAPI = {
  getAllCustomerGroup: (params) => api.get('/customer-groups', { params }),
  getCustomerGroups: () => api.get('/customer-groups/all'),
  createCustomerGroup: (data) => api.post('/customer-groups', data),
  updateCustomerGroup: (id, data) => api.put(`/customer-groups/${id}`, data),
  deleteCustomerGroup: (id) => api.delete(`/customer-groups/${id}`),

  getCustomers: (params) => api.get('/customers', { params }),
  getCustomer: (id) => api.get(`/customers/${id}`),
  createCustomer: (customerData) => api.post('/customers', customerData),
  updateCustomer: (id, customerData) => api.put(`/customers/${id}`, customerData),
  deleteCustomer: (id) => api.delete(`/customers/${id}`),
  exportCustomers: () => api.get('/customers/export', { responseType: 'blob' }),
  importCustomers: (formData) => api.post('/customers/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
}

// Health check API
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
