import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Collapse,
  Tag,
  Typography,
  Divider
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  DownOutlined,
  UpOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { Text } = Typography;

const SearchFilter = ({
  // Search configuration
  searchPlaceholder = "Tìm kiếm...",
  searchFields = [], // Array of field configs for advanced search
  
  // Filter configuration
  filters = [], // Array of filter configs
  
  // Callbacks
  onSearch,
  onFilter,
  onReset,
  
  // Initial values
  initialValues = {},
  
  // Layout
  layout = 'horizontal',
  showAdvancedSearch = true,
  showFilterCount = true,
  
  // Styling
  className,
  style
}) => {
  const [form] = Form.useForm();
  const [searchValue, setSearchValue] = useState('');
  const [isAdvancedVisible, setIsAdvancedVisible] = useState(false);
  const [activeFilters, setActiveFilters] = useState({});
  const [filterCount, setFilterCount] = useState(0);

  // Initialize form with initial values
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
      setActiveFilters(initialValues);
      updateFilterCount(initialValues);
    }
  }, [initialValues, form]);

  // Update filter count
  const updateFilterCount = (values) => {
    const count = Object.values(values).filter(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (value === null || value === undefined || value === '') return false;
      return true;
    }).length;
    setFilterCount(count);
  };

  // Handle simple search
  const handleSearch = (value) => {
    setSearchValue(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  // Handle form submit (advanced search + filters)
  const handleSubmit = (values) => {
    // Process date ranges
    const processedValues = { ...values };
    
    Object.keys(processedValues).forEach(key => {
      const value = processedValues[key];
      if (dayjs.isDayjs(value)) {
        processedValues[key] = value.format('YYYY-MM-DD');
      } else if (Array.isArray(value) && value.length === 2 && dayjs.isDayjs(value[0])) {
        processedValues[key] = [
          value[0].format('YYYY-MM-DD'),
          value[1].format('YYYY-MM-DD')
        ];
      }
    });

    setActiveFilters(processedValues);
    updateFilterCount(processedValues);
    
    if (onFilter) {
      onFilter(processedValues);
    }
  };

  // Handle reset
  const handleReset = () => {
    form.resetFields();
    setSearchValue('');
    setActiveFilters({});
    setFilterCount(0);
    
    if (onReset) {
      onReset();
    }
  };

  // Remove specific filter
  const removeFilter = (key) => {
    const newValues = { ...activeFilters };
    delete newValues[key];
    
    form.setFieldsValue({ [key]: undefined });
    setActiveFilters(newValues);
    updateFilterCount(newValues);
    
    if (onFilter) {
      onFilter(newValues);
    }
  };

  // Render filter field based on type
  const renderFilterField = (filter) => {
    const { key, label, type, options, placeholder, ...props } = filter;

    switch (type) {
      case 'select':
        return (
          <Select
            placeholder={placeholder || `Chọn ${label.toLowerCase()}`}
            allowClear
            {...props}
          >
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'multiSelect':
        return (
          <Select
            mode="multiple"
            placeholder={placeholder || `Chọn ${label.toLowerCase()}`}
            allowClear
            {...props}
          >
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'date':
        return (
          <DatePicker
            placeholder={placeholder || `Chọn ${label.toLowerCase()}`}
            style={{ width: '100%' }}
            {...props}
          />
        );

      case 'dateRange':
        return (
          <RangePicker
            placeholder={placeholder || ['Từ ngày', 'Đến ngày']}
            style={{ width: '100%' }}
            {...props}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            placeholder={placeholder || `Nhập ${label.toLowerCase()}`}
            {...props}
          />
        );

      default:
        return (
          <Input
            placeholder={placeholder || `Nhập ${label.toLowerCase()}`}
            {...props}
          />
        );
    }
  };

  // Render active filter tags
  const renderActiveFilters = () => {
    if (filterCount === 0) return null;

    const tags = Object.entries(activeFilters)
      .filter(([key, value]) => {
        if (Array.isArray(value)) return value.length > 0;
        return value !== null && value !== undefined && value !== '';
      })
      .map(([key, value]) => {
        const filter = filters.find(f => f.key === key);
        const label = filter?.label || key;
        
        let displayValue = value;
        if (Array.isArray(value)) {
          if (filter?.type === 'dateRange') {
            displayValue = `${value[0]} ~ ${value[1]}`;
          } else {
            displayValue = value.join(', ');
          }
        } else if (filter?.options) {
          const option = filter.options.find(opt => opt.value === value);
          displayValue = option?.label || value;
        }

        return (
          <Tag
            key={key}
            closable
            onClose={() => removeFilter(key)}
            style={{ marginBottom: 8 }}
          >
            {label}: {displayValue}
          </Tag>
        );
      });

    return (
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">Bộ lọc đang áp dụng:</Text>
        <div style={{ marginTop: 8 }}>
          {tags}
          {tags.length > 0 && (
            <Button
              type="link"
              size="small"
              onClick={handleReset}
              style={{ padding: 0, height: 'auto' }}
            >
              Xóa tất cả
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className={className} style={style}>
      {/* Simple Search */}
      <div style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder={searchPlaceholder}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={handleSearch}
          style={{ maxWidth: 400 }}
          enterButton={<SearchOutlined />}
        />
      </div>

      {/* Advanced Search & Filters */}
      {(showAdvancedSearch || filters.length > 0) && (
        <Collapse
          ghost
          activeKey={isAdvancedVisible ? ['1'] : []}
          onChange={(keys) => setIsAdvancedVisible(keys.includes('1'))}
        >
          <Panel
            header={
              <Space>
                <FilterOutlined />
                <span>Bộ lọc nâng cao</span>
                {showFilterCount && filterCount > 0 && (
                  <Tag color="blue">{filterCount}</Tag>
                )}
              </Space>
            }
            key="1"
            extra={
              isAdvancedVisible ? <UpOutlined /> : <DownOutlined />
            }
          >
            <Form
              form={form}
              layout={layout}
              onFinish={handleSubmit}
              initialValues={initialValues}
            >
              <Row gutter={[16, 16]}>
                {/* Advanced Search Fields */}
                {searchFields.map(field => (
                  <Col key={field.key} xs={24} sm={12} md={8} lg={6}>
                    <Form.Item
                      name={field.key}
                      label={field.label}
                    >
                      {renderFilterField(field)}
                    </Form.Item>
                  </Col>
                ))}

                {/* Filter Fields */}
                {filters.map(filter => (
                  <Col key={filter.key} xs={24} sm={12} md={8} lg={6}>
                    <Form.Item
                      name={filter.key}
                      label={filter.label}
                    >
                      {renderFilterField(filter)}
                    </Form.Item>
                  </Col>
                ))}

                {/* Action Buttons */}
                <Col xs={24}>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SearchOutlined />}
                    >
                      Tìm kiếm
                    </Button>
                    <Button
                      icon={<ClearOutlined />}
                      onClick={handleReset}
                    >
                      Xóa bộ lọc
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Panel>
        </Collapse>
      )}

      {/* Active Filters */}
      {showFilterCount && renderActiveFilters()}
    </Card>
  );
};

export default SearchFilter;
