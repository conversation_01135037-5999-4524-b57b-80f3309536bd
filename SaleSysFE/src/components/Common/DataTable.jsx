import { useState } from 'react';
import {
  Table,
  Space,
  Button,
  Tooltip,
  Popconfirm,
  Typography,
  Dropdown,
  Input,
  Select
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  SearchOutlined,
  PlusOutlined,
  FilterOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;

// Import FilterPanel
import FilterPanel from './FilterPanel';

const DataTable = ({
  // Data props
  data = [],
  columns = [],
  loading = false,

  // Header props
  title,
  showAddButton = false,
  onAdd,
  addButtonText = "Thêm mới",

  // Search props
  searchPlaceholder = "Tìm kiếm...",
  onSearch,
  showSearch = true,

  // Filter props
  filters = [],
  onFilter,

  // Advanced filter props
  advancedFilters = [],
  showAdvancedFilter = false,
  onAdvancedFilter,
  onResetAdvancedFilter,

  // Pagination props
  pagination = {},
  onPageChange,

  // Action props
  onEdit,
  onDelete,
  onView,
  actions = [],

  // Selection props
  rowSelection,

  // Table props
  rowKey = 'id',
  size = 'middle',
  bordered = false,
  scroll,

  // Style props
  className,
  style,

  ...restProps
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [showFilterPanel, setShowFilterPanel] = useState(false);

  // Generate action column
  const generateActionColumn = () => {
    const actionItems = [];

    if (onView) {
      actionItems.push({
        key: 'view',
        icon: <EyeOutlined />,
        label: 'Xem',
        onClick: onView
      });
    }

    if (onEdit) {
      actionItems.push({
        key: 'edit',
        icon: <EditOutlined />,
        label: 'Sửa',
        onClick: onEdit
      });
    }

    if (onDelete) {
      actionItems.push({
        key: 'delete',
        icon: <DeleteOutlined />,
        label: 'Xóa',
        onClick: onDelete,
        danger: true,
        confirm: true
      });
    }

    // Add custom actions
    actionItems.push(...actions);

    if (actionItems.length === 0) return null;

    return {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        if (actionItems.length <= 3) {
          return (
            <Space size="small">
              {actionItems.map((action) => {
                if (action.confirm) {
                  return (
                    <Popconfirm
                      key={action.key}
                      title={`Bạn có chắc chắn muốn ${action.label.toLowerCase()}?`}
                      onConfirm={() => action.onClick(record)}
                      okText="Có"
                      cancelText="Không"
                    >
                      <Tooltip title={action.label}>
                        <Button
                          type="text"
                          size="small"
                          icon={action.icon}
                          danger={action.danger}
                        />
                      </Tooltip>
                    </Popconfirm>
                  );
                }

                return (
                  <Tooltip key={action.key} title={action.label}>
                    <Button
                      type="text"
                      size="small"
                      icon={action.icon}
                      onClick={() => action.onClick(record)}
                      danger={action.danger}
                    />
                  </Tooltip>
                );
              })}
            </Space>
          );
        }

        // Use dropdown for many actions
        const menuItems = actionItems.map((action) => ({
          key: action.key,
          icon: action.icon,
          label: action.label,
          danger: action.danger,
          onClick: () => action.onClick(record)
        }));

        return (
          <Dropdown
            menu={{ items: menuItems }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        );
      }
    };
  };

  // Prepare columns with action column
  const tableColumns = [...columns];
  const actionColumn = generateActionColumn();
  if (actionColumn) {
    tableColumns.push(actionColumn);
  }

  // Handle row selection
  const handleRowSelection = rowSelection ? {
    selectedRowKeys,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      if (rowSelection.onChange) {
        rowSelection.onChange(keys, rows);
      }
    },
    ...rowSelection
  } : undefined;

  // Handle pagination
  const paginationConfig = pagination === false ? false : {
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) =>
      `${range[0]}-${range[1]} của ${total} mục`,
    onChange: onPageChange,
    onShowSizeChange: onPageChange,
    ...pagination
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchValue(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  return (
    <div className={className} style={style}>
      {/* Header Section */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
        padding: '0 0 16px 0',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <div>
          {title && (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              {title}
            </Title>
          )}
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          {/* Search */}
          {showSearch && (
            <Input
              placeholder={searchPlaceholder}
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
          )}

          {/* Filters */}
          {filters.map((filter) => (
            <Select
              key={filter.key}
              placeholder={filter.placeholder}
              style={{ width: 150 }}
              allowClear
              onChange={(value) => onFilter && onFilter(filter.key, value)}
            >
              {filter.options?.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          ))}

          {/* Advanced Filter Button */}
          {showAdvancedFilter && advancedFilters.length > 0 && (
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowFilterPanel(true)}
            >
              Bộ lọc
            </Button>
          )}

          {/* Add Button */}
          {showAddButton && onAdd && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onAdd}
            >
              {addButtonText}
            </Button>
          )}
        </div>
      </div>

      {/* Table */}
      <Table
        dataSource={data}
        columns={tableColumns}
        loading={loading}
        pagination={paginationConfig}
        rowSelection={handleRowSelection}
        rowKey={rowKey}
        size={size}
        bordered={bordered}
        scroll={scroll}
        {...restProps}
      />

      {/* Advanced Filter Panel */}
      {showAdvancedFilter && (
        <FilterPanel
          title="Bộ lọc nâng cao"
          visible={showFilterPanel}
          filters={advancedFilters}
          onFilter={(values) => {
            if (onAdvancedFilter) {
              onAdvancedFilter(values);
            }
          }}
          onReset={() => {
            if (onResetAdvancedFilter) {
              onResetAdvancedFilter();
            }
          }}
          onClose={() => setShowFilterPanel(false)}
        />
      )}
    </div>
  );
};

export default DataTable;
