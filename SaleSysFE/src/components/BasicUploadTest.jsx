import React, { useState } from 'react';
import { Upload, Button, message, Card } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const BasicUploadTest = () => {
  const [fileList, setFileList] = useState([]);

  const handleChange = ({ fileList: newFileList }) => {
    console.log('📋 Upload onChange called:', newFileList);
    setFileList(newFileList);
  };

  const customRequest = async ({ file, onSuccess, onError }) => {
    console.log('🎯 customRequest called with file:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    try {
      // Real upload to backend
      const formData = new FormData();
      formData.append('image', file);
      formData.append('folder', 'basic-test');
      formData.append('width', '400');
      formData.append('height', '300');

      console.log('📤 Uploading to backend...');

      const response = await fetch('http://localhost:5000/api/upload/simple', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Upload failed:', errorText);
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Upload success:', data);

      if (data.success) {
        onSuccess({
          url: data.data.url,
          thumbnail_url: data.data.thumbnail_url,
          public_id: data.data.public_id,
          name: file.name
        });
      } else {
        throw new Error(data.message || 'Upload failed');
      }

    } catch (error) {
      console.error('❌ Upload error:', error);
      onError(error);
    }
  };

  const beforeUpload = (file) => {
    console.log('📁 beforeUpload called:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Please select an image file!');
      return false;
    }

    console.log('✅ File validation passed');
    return true;
  };

  return (
    <Card title="🔧 Basic Upload Test" style={{ maxWidth: 600, margin: '20px auto' }}>
      <div style={{ marginBottom: 20 }}>
        <p><strong>Purpose:</strong> Test basic Antd Upload functionality</p>
        <p><strong>Instructions:</strong> Select an image file and watch console logs</p>
      </div>

      <Upload
        listType="picture-card"
        fileList={fileList}
        onChange={handleChange}
        customRequest={customRequest}
        beforeUpload={beforeUpload}
        maxCount={3}
      >
        {fileList.length >= 3 ? null : (
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>Upload</div>
          </div>
        )}
      </Upload>

      <div style={{ marginTop: 20 }}>
        <h4>📊 Current State:</h4>
        <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
          <p><strong>File Count:</strong> {fileList.length}</p>
          {fileList.map((file, index) => (
            <div key={file.uid} style={{ fontSize: 12, marginBottom: 4 }}>
              <strong>File {index + 1}:</strong> {file.name} - Status: {file.status}
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: 20, fontSize: 12, color: '#666' }}>
        <p><strong>Expected Console Logs:</strong></p>
        <ol>
          <li>📁 beforeUpload called</li>
          <li>✅ File validation passed</li>
          <li>🎯 customRequest called</li>
          <li>📋 Upload onChange called</li>
          <li>✅ Simulated upload success</li>
        </ol>
      </div>
    </Card>
  );
};

export default BasicUploadTest;
