import React, { useState } from 'react';
import { Card, Button, message, Space, Divider } from 'antd';
import ImageUpload from '../ImageUpload/ImageUpload';
import ImageSelector from '../ImageSelector/ImageSelector';

const TestUpload = () => {
  const [productImages, setProductImages] = useState([]);
  const [showSelector, setShowSelector] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const handleImagesChange = (images) => {
    setProductImages(images);
    console.log('Product images updated:', images);
  };

  const handleImageSelect = (image) => {
    setSelectedImage(image);
    setShowSelector(false);
    message.success(`Đã chọn ảnh: ${image.name}`);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="🧪 Test Upload Ảnh với Cloudinary" style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <h3>1. Upload ảnh sản phẩm:</h3>
            <ImageUpload
              value={productImages}
              onChange={handleImagesChange}
              maxCount={5}
              folder="test-products"
              width={600}
              height={400}
            />
          </div>

          <Divider />

          <div>
            <h3>2. Test chọn ảnh từ danh sách:</h3>
            <Button 
              type="primary" 
              onClick={() => setShowSelector(true)}
              disabled={productImages.length === 0}
            >
              Mở Image Selector
            </Button>
            {selectedImage && (
              <div style={{ marginTop: 16 }}>
                <p><strong>Ảnh đã chọn:</strong> {selectedImage.name}</p>
                <img 
                  src={selectedImage.thumbnail_url || selectedImage.url} 
                  alt="Selected" 
                  style={{ width: 100, height: 100, objectFit: 'cover', borderRadius: 8 }}
                />
              </div>
            )}
          </div>

          <Divider />

          <div>
            <h3>3. Thông tin ảnh đã upload:</h3>
            {productImages.length > 0 ? (
              <div style={{ background: '#f9f9f9', padding: 16, borderRadius: 8 }}>
                <pre>{JSON.stringify(productImages, null, 2)}</pre>
              </div>
            ) : (
              <p style={{ color: '#999' }}>Chưa có ảnh nào được upload</p>
            )}
          </div>
        </Space>
      </Card>

      <ImageSelector
        visible={showSelector}
        onCancel={() => setShowSelector(false)}
        onSelect={handleImageSelect}
        images={productImages}
        selectedImage={selectedImage}
        title="Chọn ảnh test"
      />
    </div>
  );
};

export default TestUpload;
