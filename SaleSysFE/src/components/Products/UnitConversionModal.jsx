import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  Typography,
  Table,
  message,
  Divider,
  Select
} from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

const UnitConversionModal = ({
  visible,
  onCancel,
  onSave,
  editingConversion
}) => {
  const [form] = Form.useForm();
  const [conversions, setConversions] = useState([]);
  const [editingIndex, setEditingIndex] = useState(null);

  // Common units
  const commonUnits = [
    'cái', 'chiếc', 'bộ', 'đôi', 'cặp',
    'kg', 'gram', 'tấn', 'tạ',
    'lít', 'ml', 'gallon',
    'mét', 'cm', 'mm', 'km',
    'thùng', 'hộp', 'gói', 'túi', 'chai'
  ];

  useEffect(() => {
    if (visible) {
      if (editingConversion) {
        setConversions(editingConversion.quy_doi || []);
      } else {
        setConversions([]);
      }
      form.resetFields();
    }
  }, [visible, editingConversion, form]);

  const handleSave = async () => {
    try {
      if (conversions.length === 0) {
        message.error('Vui lòng thêm ít nhất một đơn vị quy đổi');
        return;
      }

      const conversionData = {
        quy_doi: conversions
      };

      onSave(conversionData, editingConversion?.index);
      handleCancel();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setConversions([]);
    setEditingIndex(null);
    onCancel();
  };

  const addConversion = async () => {
    try {
      const values = await form.validateFields();
      
      // Check if unit already exists
      const existingUnit = conversions.find(conv => conv.don_vi === values.don_vi);
      if (existingUnit && editingIndex === null) {
        message.error('Đơn vị này đã tồn tại');
        return;
      }

      const newConversion = {
        don_vi: values.don_vi,
        ty_le_quy_doi: values.ty_le_quy_doi,
        mo_ta: values.mo_ta || ''
      };

      if (editingIndex !== null) {
        // Edit existing conversion
        const newConversions = [...conversions];
        newConversions[editingIndex] = newConversion;
        setConversions(newConversions);
        setEditingIndex(null);
      } else {
        // Add new conversion
        setConversions([...conversions, newConversion]);
      }

      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const editConversion = (conversion, index) => {
    form.setFieldsValue({
      don_vi: conversion.don_vi,
      ty_le_quy_doi: conversion.ty_le_quy_doi,
      mo_ta: conversion.mo_ta
    });
    setEditingIndex(index);
  };

  const deleteConversion = (index) => {
    const newConversions = conversions.filter((_, i) => i !== index);
    setConversions(newConversions);
    
    if (editingIndex === index) {
      form.resetFields();
      setEditingIndex(null);
    }
  };

  const cancelEdit = () => {
    form.resetFields();
    setEditingIndex(null);
  };

  const columns = [
    {
      title: 'Đơn vị',
      dataIndex: 'don_vi',
      key: 'don_vi',
      width: 120
    },
    {
      title: 'Tỷ lệ quy đổi',
      dataIndex: 'ty_le_quy_doi',
      key: 'ty_le_quy_doi',
      width: 150,
      render: (value, record) => (
        <Text>
          1 {record.don_vi} = {value} đơn vị cơ bản
        </Text>
      )
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta'
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_, record, index) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => editConversion(record, index)}
            size="small"
          >
            Sửa
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => deleteConversion(index)}
            size="small"
          >
            Xóa
          </Button>
        </Space>
      )
    }
  ];

  return (
    <Modal
      title="Quản lý đơn vị quy đổi"
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu
        </Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          Tạo ra quy đổi cho các đơn vị khác nhau. Ví dụ: 1 thùng = 24 chai, 1 kg = 1000 gram
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
      >
        <div style={{ background: '#f6f6f6', padding: 16, borderRadius: 6, marginBottom: 16 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 16 }}>
            {editingIndex !== null ? 'Sửa đơn vị quy đổi' : 'Thêm đơn vị quy đổi'}
          </Title>
          
          <Space.Compact style={{ width: '100%', marginBottom: 12 }}>
            <Form.Item
              name="don_vi"
              style={{ flex: 1, marginBottom: 0 }}
              rules={[{ required: true, message: 'Vui lòng nhập đơn vị' }]}
            >
              <Select
                placeholder="Chọn hoặc nhập đơn vị"
                showSearch
                allowClear
                style={{ width: '100%' }}
              >
                {commonUnits.map(unit => (
                  <Option key={unit} value={unit}>{unit}</Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="ty_le_quy_doi"
              style={{ width: 150, marginBottom: 0 }}
              rules={[
                { required: true, message: 'Vui lòng nhập tỷ lệ' },
                { type: 'number', min: 0.001, message: 'Tỷ lệ phải lớn hơn 0' }
              ]}
            >
              <InputNumber
                placeholder="Tỷ lệ"
                style={{ width: '100%' }}
                min={0.001}
                step={0.001}
              />
            </Form.Item>
            
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={addConversion}
            >
              {editingIndex !== null ? 'Cập nhật' : 'Thêm'}
            </Button>
            
            {editingIndex !== null && (
              <Button onClick={cancelEdit}>
                Hủy
              </Button>
            )}
          </Space.Compact>
          
          <Form.Item
            name="mo_ta"
            style={{ marginBottom: 0 }}
          >
            <Input placeholder="Mô tả (tùy chọn)" />
          </Form.Item>
        </div>
      </Form>

      <Divider>Danh sách đơn vị quy đổi</Divider>

      {conversions.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <Text>Chưa có đơn vị quy đổi nào</Text>
        </div>
      ) : (
        <Table
          dataSource={conversions}
          columns={columns}
          pagination={false}
          size="small"
          rowKey={(record, index) => index}
        />
      )}

      <div style={{ background: '#f6f6f6', padding: 12, borderRadius: 6, marginTop: 16 }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          <strong>Lưu ý:</strong> Đơn vị cơ bản là đơn vị nhỏ nhất để tính toán. 
          Tỷ lệ quy đổi cho biết 1 đơn vị lớn bằng bao nhiêu đơn vị cơ bản.
          Ví dụ: 1 thùng = 24 chai, thì tỷ lệ quy đổi là 24.
        </Text>
      </div>
    </Modal>
  );
};

export default UnitConversionModal;
