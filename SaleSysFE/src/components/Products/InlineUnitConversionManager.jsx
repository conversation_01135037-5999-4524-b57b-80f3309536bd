import React, { useState } from 'react';
import {
  Card,
  Switch,
  Typography,
  Row,
  Col,
  Select,
  InputNumber,
  Space,
  Button,
  Tooltip,
  message
} from 'antd';
import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

const InlineUnitConversionManager = ({ 
  enabled = false,
  onEnabledChange,
  conversions = [],
  onConversionsChange,
  variants = []
}) => {
  const [newConversion, setNewConversion] = useState({
    phien_ban_id: null,
    don_vi: '',
    so_luong: 1
  });

  // Common units
  const commonUnits = [
    'cái', 'chiếc', 'bộ', 'đôi', 'cặp',
    'kg', 'gram', 'tấn', 'tạ',
    'lít', 'ml', 'gallon',
    'mét', 'cm', 'mm', 'km',
    'thùng', 'hộp', 'gói', 'túi', 'chai'
  ];

  const handleEnabledChange = (checked) => {
    onEnabledChange(checked);
    if (!checked) {
      // Clear conversions when disabled
      onConversionsChange([]);
    }
  };

  const addConversion = () => {
    if (!newConversion.phien_ban_id) {
      message.error('Vui lòng chọn phiên bản sản phẩm');
      return;
    }
    if (!newConversion.don_vi) {
      message.error('Vui lòng nhập đơn vị quy đổi');
      return;
    }
    if (!newConversion.so_luong || newConversion.so_luong <= 0) {
      message.error('Vui lòng nhập số lượng quy đổi hợp lệ');
      return;
    }

    // Check if conversion already exists for this variant and unit
    const existingConversion = conversions.find(
      conv => conv.phien_ban_id === newConversion.phien_ban_id && 
              conv.don_vi === newConversion.don_vi
    );

    if (existingConversion) {
      message.error('Đơn vị quy đổi này đã tồn tại cho phiên bản này');
      return;
    }

    const newConversions = [...conversions, { ...newConversion }];
    onConversionsChange(newConversions);

    // Reset form
    setNewConversion({
      phien_ban_id: null,
      don_vi: '',
      so_luong: 1
    });
  };

  const removeConversion = (index) => {
    const newConversions = conversions.filter((_, i) => i !== index);
    onConversionsChange(newConversions);
  };

  const getVariantName = (variantId) => {
    const variant = variants.find(v => v.id === variantId);
    return variant ? variant.ten_phien_ban : 'Phiên bản không xác định';
  };

  return (
    <Card 
      title={
        <Space>
          <span>Thêm đơn vị quy đổi</span>
          <Tooltip title="Tạo ra quy đổi các đơn vị tính khác nhau">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      }
      style={{ marginBottom: 24 }}
    >
      <div style={{ marginBottom: 16 }}>
        <Space align="start">
          <Switch 
            checked={enabled}
            onChange={handleEnabledChange}
          />
          <div>
            <Text style={{ fontWeight: 500 }}>
              Tạo ra quy đổi các đơn vị tính khác nhau
            </Text>
          </div>
        </Space>
      </div>

      {enabled && (
        <div style={{ 
          background: '#fafafa',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #e8e8e8'
        }}>
          {/* Header Row */}
          <Row gutter={16} style={{ marginBottom: 12 }}>
            <Col span={8}>
              <Text strong style={{ fontSize: '14px' }}>Phiên bản sản phẩm</Text>
            </Col>
            <Col span={8}>
              <Text strong style={{ fontSize: '14px' }}>Đơn vị quy đổi</Text>
            </Col>
            <Col span={6}>
              <Text strong style={{ fontSize: '14px' }}>Số lượng</Text>
            </Col>
            <Col span={2}></Col>
          </Row>

          {/* Add New Conversion Row */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Select
                value={newConversion.phien_ban_id}
                onChange={(value) => setNewConversion(prev => ({ ...prev, phien_ban_id: value }))}
                placeholder="Chọn phiên bản"
                style={{ width: '100%' }}
                allowClear
              >
                {Array.isArray(variants) && variants.map((variant, index) => (
                  <Option key={index} value={index}>
                    {variant?.ten_phien_ban}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <Select
                value={newConversion.don_vi}
                onChange={(value) => setNewConversion(prev => ({ ...prev, don_vi: value }))}
                placeholder="Nhập tên đơn vị"
                style={{ width: '100%' }}
                showSearch
                allowClear
                mode="combobox"
              >
                {commonUnits.map(unit => (
                  <Option key={unit} value={unit}>{unit}</Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <InputNumber
                value={newConversion.so_luong}
                onChange={(value) => setNewConversion(prev => ({ ...prev, so_luong: value }))}
                placeholder="Quy đổi tương ứng"
                style={{ width: '100%' }}
                min={0.001}
                step={0.001}
                precision={3}
              />
            </Col>
            <Col span={2}>
              <Button
                type="link"
                icon={<PlusOutlined />}
                onClick={addConversion}
                style={{
                  width: '100%',
                  color: '#1890ff',
                  fontSize: '12px',
                  padding: '4px 8px'
                }}
              >
                Thêm đơn vị khác
              </Button>
            </Col>
          </Row>

          {/* Existing Conversions */}
          {Array.isArray(conversions) && conversions.map((conversion, index) => (
            <Row key={index} gutter={16} style={{ marginBottom: 8 }}>
              <Col span={8}>
                <div style={{
                  padding: '8px 12px',
                  background: '#f5f5f5',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <Text>{getVariantName(conversion.phien_ban_id)}</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{
                  padding: '8px 12px',
                  background: '#f5f5f5',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <Text>{conversion.don_vi}</Text>
                </div>
              </Col>
              <Col span={6}>
                <div style={{
                  padding: '8px 12px',
                  background: '#f5f5f5',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <Text>{conversion.so_luong}</Text>
                </div>
              </Col>
              <Col span={2}>
                <Button 
                  type="text" 
                  danger
                  onClick={() => removeConversion(index)}
                  style={{ width: '100%' }}
                >
                  ×
                </Button>
              </Col>
            </Row>
          ))}

          {(!Array.isArray(conversions) || conversions.length === 0) && (
            <div style={{ 
              textAlign: 'center', 
              padding: '20px 0', 
              color: '#999',
              fontStyle: 'italic'
            }}>
              Chưa có đơn vị quy đổi nào
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default InlineUnitConversionManager;
