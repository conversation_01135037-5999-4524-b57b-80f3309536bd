import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Row,
  Col,
  Button,
  Space,
  Divider,
  Typography,
  Upload,
  message
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

const VariantModal = ({
  visible,
  onCancel,
  onSave,
  editingVariant,
  attributes = []
}) => {
  const [form] = Form.useForm();
  const [selectedAttributes, setSelectedAttributes] = useState([]);
  const [variantImage, setVariantImage] = useState([]);

  useEffect(() => {
    if (visible) {
      if (editingVariant) {
        // Edit mode
        form.setFieldsValue({
          ten_phien_ban: editingVariant.ten_phien_ban,
          ma: editingVariant.ma,
          ma_vach: editingVariant.ma_vach,
          mo_ta: editingVariant.mo_ta,
          khoi_luong: editingVariant.khoi_luong,
          don_vi_tinh: editingVariant.don_vi_tinh,
          gia_le: editingVariant.gia_le,
          gia_buon: editingVariant.gia_buon,
          gia_nhap: editingVariant.gia_nhap
        });
        setSelectedAttributes(editingVariant.thuoc_tinh || []);
        setVariantImage(editingVariant.anh_phien_ban || []);
      } else {
        // Create mode
        form.resetFields();
        setSelectedAttributes([]);
        setVariantImage([]);
      }
    }
  }, [visible, editingVariant, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      const variantData = {
        ...values,
        thuoc_tinh: selectedAttributes,
        anh_phien_ban: variantImage
      };

      onSave(variantData, editingVariant?.index);
      handleCancel();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedAttributes([]);
    setVariantImage([]);
    onCancel();
  };

  const addAttribute = () => {
    setSelectedAttributes([...selectedAttributes, { thuoc_tinh_id: null, gia_tri: '' }]);
  };

  const removeAttribute = (index) => {
    const newAttributes = selectedAttributes.filter((_, i) => i !== index);
    setSelectedAttributes(newAttributes);
  };

  const updateAttribute = (index, field, value) => {
    const newAttributes = [...selectedAttributes];
    newAttributes[index] = { ...newAttributes[index], [field]: value };
    
    // If attribute changed, reset value
    if (field === 'thuoc_tinh_id') {
      newAttributes[index].gia_tri = '';
    }
    
    setSelectedAttributes(newAttributes);
  };

  const getAttributeValues = (attributeId) => {
    const attribute = attributes.find(attr => attr.id === attributeId);
    return attribute ? attribute.gia_tri : [];
  };

  const handleImageUpload = ({ fileList }) => {
    setVariantImage(fileList);
  };

  return (
    <Modal
      title={editingVariant ? "Sửa phiên bản sản phẩm" : "Thêm phiên bản sản phẩm"}
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          {editingVariant ? "Cập nhật" : "Thêm"}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          don_vi_tinh: 'cái',
          gia_le: 0,
          gia_buon: 0,
          gia_nhap: 0
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="ten_phien_ban"
              label="Tên phiên bản"
              rules={[{ required: true, message: 'Vui lòng nhập tên phiên bản' }]}
            >
              <Input placeholder="Nhập tên phiên bản" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="ma"
              label="Mã sản phẩm"
            >
              <Input placeholder="Để trống sẽ tự động sinh mã" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="ma_vach"
              label="Mã vạch"
            >
              <Input placeholder="Nhập mã vạch" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="don_vi_tinh"
              label="Đơn vị tính"
            >
              <Select placeholder="Chọn đơn vị tính">
                <Option value="cái">Cái</Option>
                <Option value="chiếc">Chiếc</Option>
                <Option value="bộ">Bộ</Option>
                <Option value="kg">Kg</Option>
                <Option value="gram">Gram</Option>
                <Option value="lít">Lít</Option>
                <Option value="ml">ML</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="mo_ta"
          label="Mô tả"
        >
          <Input.TextArea rows={3} placeholder="Nhập mô tả phiên bản" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="gia_le"
              label="Giá lẻ"
              rules={[{ required: true, message: 'Vui lòng nhập giá lẻ' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="0"
                min={0}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="gia_buon"
              label="Giá buôn"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="0"
                min={0}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="gia_nhap"
              label="Giá nhập"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="0"
                min={0}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="khoi_luong"
          label="Khối lượng (gram)"
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="0"
            min={0}
          />
        </Form.Item>

        <Divider>Thuộc tính phiên bản</Divider>
        
        <div style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {selectedAttributes.map((attr, index) => (
              <Row key={index} gutter={8} align="middle">
                <Col span={10}>
                  <Select
                    placeholder="Chọn thuộc tính"
                    value={attr.thuoc_tinh_id}
                    onChange={(value) => updateAttribute(index, 'thuoc_tinh_id', value)}
                    style={{ width: '100%' }}
                  >
                    {attributes.map(attribute => (
                      <Option key={attribute.id} value={attribute.id}>
                        {attribute.ten}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={10}>
                  <Select
                    placeholder="Chọn giá trị"
                    value={attr.gia_tri}
                    onChange={(value) => updateAttribute(index, 'gia_tri', value)}
                    style={{ width: '100%' }}
                    disabled={!attr.thuoc_tinh_id}
                  >
                    {getAttributeValues(attr.thuoc_tinh_id).map(value => (
                      <Option key={value} value={value}>
                        {value}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={4}>
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeAttribute(index)}
                  />
                </Col>
              </Row>
            ))}
          </Space>
          
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addAttribute}
            style={{ width: '100%', marginTop: 8 }}
          >
            Thêm thuộc tính
          </Button>
        </div>

        <Divider>Ảnh phiên bản</Divider>
        
        <Upload
          listType="picture-card"
          fileList={variantImage}
          onChange={handleImageUpload}
          beforeUpload={() => false}
          maxCount={5}
        >
          {variantImage.length < 5 && (
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Tải ảnh</div>
            </div>
          )}
        </Upload>
      </Form>
    </Modal>
  );
};

export default VariantModal;
