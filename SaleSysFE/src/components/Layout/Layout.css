/* Layout.css - Styles for the main layout component */

/* Fixed header styles */
.fixed-header {
  position: fixed !important;
  top: 0;
  right: 0;
  z-index: 1000;
  transition: left 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Sidebar styles */
.fixed-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
}

/* Content area with proper spacing for fixed header */
.main-content {
  margin-top: 64px; /* Height of header */
  transition: margin-left 0.2s ease;
}

/* Logo area */
.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  background: #001529;
}

.logo-text {
  color: white !important;
  margin: 0 !important;
  transition: font-size 0.2s ease;
}

/* User dropdown styles */
.user-dropdown {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-dropdown:hover {
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px !important;
  font-weight: 600 !important;
  line-height: 1.2;
}

.user-role {
  font-size: 12px !important;
  line-height: 1.2;
  margin-top: 2px;
}

/* Breadcrumb styles */
.breadcrumb-container {
  margin-bottom: 16px;
  padding: 8px 0;
}

/* Page content container */
.page-content {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  min-height: calc(100vh - 200px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Menu styles */
.sidebar-menu {
  border-right: 0 !important;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.sidebar-menu::-webkit-scrollbar {
  width: 6px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: #001529;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #1890ff;
  border-radius: 3px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #40a9ff;
}

/* Notification badge */
.notification-badge {
  font-size: 16px;
}

/* Toggle button */
.sidebar-toggle {
  font-size: 16px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
    margin-top: 64px;
  }
  
  .fixed-header {
    left: 0 !important;
  }
  
  .fixed-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .fixed-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .page-content {
    padding: 16px;
    margin: 16px;
    min-height: calc(100vh - 120px);
  }
  
  .user-info {
    display: none;
  }
  
  .logo-text {
    font-size: 16px !important;
  }
}

@media (max-width: 576px) {
  .page-content {
    padding: 12px;
    margin: 12px;
    border-radius: 6px;
  }
  
  .breadcrumb-container {
    margin-bottom: 12px;
  }
  
  .sidebar-toggle {
    width: 36px !important;
    height: 36px !important;
  }
}

/* Animation for smooth transitions */
.layout-transition {
  transition: all 0.2s ease;
}

/* Header content alignment */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Menu item hover effects */
.ant-menu-dark .ant-menu-item:hover,
.ant-menu-dark .ant-menu-submenu-title:hover {
  background-color: #1890ff !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

/* Content area background */
.content-background {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  padding: 24px;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Error states */
.error-boundary {
  padding: 24px;
  text-align: center;
  background: #fff;
  border-radius: 8px;
  margin: 24px;
}

/* Accessibility improvements */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #1890ff;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 2000;
}

.skip-link:focus {
  top: 6px;
}

/* Print styles */
@media print {
  .fixed-sidebar,
  .fixed-header {
    display: none !important;
  }
  
  .main-content {
    margin: 0 !important;
  }
  
  .page-content {
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
}
