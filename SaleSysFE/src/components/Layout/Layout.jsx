import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Typography,
  Space,
  Badge,
  Breadcrumb
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  DashboardOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  HomeOutlined,
  BarChartOutlined
} from '@ant-design/icons';

import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../contexts/PermissionContext';

const { Header, Sider, Content } = AntLayout;
const { Title, Text } = Typography;

// CSS styles for fixed layout
const layoutStyles = `
  .fixed-sidebar {
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1001;
    overflow-x: hidden;
    transition: all 0.2s ease;
  }

  .fixed-header {
    position: fixed !important;
    top: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .main-content {
    transition: margin-left 0.2s ease;
    min-height: 100vh;
  }

  .content-background {
    margin-top: 64px;
    padding: 24px;
    min-height: calc(100vh - 64px);
    background-color: #f0f2f5;
  }

  .page-content {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 24px;
    min-height: calc(100vh - 200px);
  }

  .logo-container {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 16px;
    transition: all 0.2s ease;
  }

  .logo-text {
    color: #fff !important;
    margin: 0 !important;
    font-weight: bold;
    transition: all 0.2s ease;
  }

  .sidebar-menu {
    border-right: none;
    height: calc(100vh - 64px);
    overflow-y: auto;
  }

  .sidebar-menu::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-menu::-webkit-scrollbar-track {
    background: #001529;
  }

  .sidebar-menu::-webkit-scrollbar-thumb {
    background: #1890ff;
    border-radius: 3px;
  }

  .user-dropdown {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  .user-dropdown:hover {
    background-color: #f5f5f5;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .user-name {
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1.2;
  }

  .user-role {
    font-size: 12px !important;
    line-height: 1.2;
    margin-top: 2px;
  }

  .breadcrumb-container {
    margin-bottom: 16px;
    padding: 8px 0;
  }

  .sidebar-toggle {
    font-size: 16px !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  @media (max-width: 768px) {
    .main-content {
      margin-left: 0 !important;
      margin-top: 64px;
    }

    .fixed-header {
      left: 0 !important;
      width: 100% !important;
    }

    .fixed-sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    .user-info {
      display: none;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.getElementById('layout-styles');
  if (!styleElement) {
    const style = document.createElement('style');
    style.id = 'layout-styles';
    style.textContent = layoutStyles;
    document.head.appendChild(style);
  }
}

// Icon mapping for menu items
const iconMap = {
  DashboardOutlined: <DashboardOutlined />,
  ShoppingOutlined: <ShoppingOutlined />,
  ShoppingCartOutlined: <ShoppingCartOutlined />,
  UserOutlined: <UserOutlined />,
  HomeOutlined: <HomeOutlined />,
  BarChartOutlined: <BarChartOutlined />,
  SettingOutlined: <SettingOutlined />
};

const Layout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState([]);
  const { user, logout } = useAuth();
  const { getAccessibleMenuItems, canAccessModule } = usePermissions();
  const navigate = useNavigate();
  const location = useLocation();

  // Get menu items based on user permissions
  const rawMenuItems = getAccessibleMenuItems();

  const menuItems = rawMenuItems.map(item => {
    const hasChildren = item.children && item.children.length > 0;

    const menuItem = {
      key: item.key,
      icon: iconMap[item.icon] || <SettingOutlined />,
      label: item.label
    };

    // Nếu có children, không thêm onClick
    if (hasChildren) {
      menuItem.children = item.children.map(child => ({
        key: child.key,
        label: child.label
        // Không cần onClick ở đây, sẽ xử lý trong onSelect
      }));
    } else if (item.path) {
      // Nếu không có children và có path, thêm onClick
      menuItem.onClick = () => navigate(item.path);
    }

    return menuItem;
  });

  // Get current selected menu key
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    if (path.startsWith('/dashboard')) return ['dashboard'];
    if (path === '/products') return ['products-list'];
    if (path.startsWith('/products/categories')) return ['product-categories'];
    if (path.startsWith('/products/brands')) return ['product-brands'];
    if (path.startsWith('/products/attributes')) return ['product-attributes'];
    if (path.startsWith('/products/variants')) return ['product-variants'];
    if (path.startsWith('/products')) return ['products-list'];
    if (path.startsWith('/orders')) return ['orders'];
    if (path === '/customers') return ['customers-list'];
    if (path.startsWith('/customers/groups')) return ['customer-groups'];
    if (path.startsWith('/customers')) return ['customers-list'];
    if (path === '/warehouses') return ['warehouses-list'];
    if (path.startsWith('/warehouses/inventory')) return ['inventory'];
    if (path.startsWith('/warehouses/movements')) return ['stock-movements'];
    if (path.startsWith('/warehouses/check')) return ['stock-check'];
    if (path.startsWith('/warehouses')) return ['warehouses-list'];
    if (path.startsWith('/reports')) return ['reports'];
    if (path.startsWith('/users')) return ['users'];
    if (path.startsWith('/roles')) return ['roles'];
    if (path.startsWith('/permissions')) return ['permissions'];
    return ['dashboard'];
  };

  // Initialize open keys based on current path
  useEffect(() => {
    const path = location.pathname;
    const newOpenKeys = [];
    if (path.startsWith('/products')) newOpenKeys.push('products');
    if (path.startsWith('/customers')) newOpenKeys.push('customers');
    if (path.startsWith('/warehouses')) newOpenKeys.push('warehouses');
    if (path.startsWith('/users') || path.startsWith('/roles') || path.startsWith('/permissions')) {
      newOpenKeys.push('system');
    }
    setOpenKeys(newOpenKeys);
  }, [location.pathname]);

  // Handle submenu open/close
  const handleOpenChange = (keys) => {
    setOpenKeys(keys);
  };

  // Generate breadcrumb items
  const getBreadcrumbItems = () => {
    const path = location.pathname;
    const items = [{ title: 'Trang chủ' }];

    if (path.startsWith('/dashboard')) {
      items.push({ title: 'Tổng quan' });
    } else if (path === '/products') {
      items.push({ title: 'Sản phẩm' }, { title: 'Danh sách sản phẩm' });
    } else if (path.startsWith('/products/categories')) {
      items.push({ title: 'Sản phẩm' }, { title: 'Loại sản phẩm' });
    } else if (path.startsWith('/products/brands')) {
      items.push({ title: 'Sản phẩm' }, { title: 'Nhãn hiệu' });
    } else if (path.startsWith('/products/attributes')) {
      items.push({ title: 'Sản phẩm' }, { title: 'Thuộc tính' });
    } else if (path.startsWith('/products/variants')) {
      items.push({ title: 'Sản phẩm' }, { title: 'Phiên bản sản phẩm' });
    } else if (path.startsWith('/products')) {
      items.push({ title: 'Sản phẩm' });
    } else if (path.startsWith('/orders')) {
      items.push({ title: 'Đơn hàng' });
      //khách hàng
    } else if (path === '/customers') {
      items.push({ title: 'Khách hàng' }, { title: 'Danh sách khách hàng' });
    } else if (path.startsWith('/customers/groups')) {
      items.push({ title: 'Khách hàng' }, { title: 'Nhóm khách hàng' });
    } else if (path.startsWith('/customers')) {
      items.push({ title: 'Khách hàng' });
      //end khách hàng
    } else if (path === '/warehouses') {
      items.push({ title: 'Kho hàng' }, { title: 'Danh sách kho hàng' });
    } else if (path.startsWith('/warehouses/inventory')) {
      items.push({ title: 'Kho hàng' }, { title: 'Quản lý tồn kho' });
    } else if (path.startsWith('/warehouses/movements')) {
      items.push({ title: 'Kho hàng' }, { title: 'Xuất nhập kho' });
    } else if (path.startsWith('/warehouses/check')) {
      items.push({ title: 'Kho hàng' }, { title: 'Kiểm kê kho' });
    } else if (path.startsWith('/warehouses')) {
      items.push({ title: 'Kho hàng' });
    } else if (path.startsWith('/reports')) {
      items.push({ title: 'Báo cáo' });
    } else if (path.startsWith('/users')) {
      items.push({ title: 'Hệ thống' }, { title: 'Người dùng' });
    } else if (path.startsWith('/roles')) {
      items.push({ title: 'Hệ thống' }, { title: 'Vai trò' });
    } else if (path.startsWith('/permissions')) {
      items.push({ title: 'Hệ thống' }, { title: 'Quyền' });
    }

    return items;
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Cài đặt',
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: logout
    }
  ];

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      {/* Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="fixed-sidebar"
        style={{
          height: '100vh',
        }}
      >
        {/* Logo */}
        <div className="logo-container">
          <Title
            level={4}
            className="logo-text"
            style={{
              fontSize: collapsed ? 16 : 20
            }}
          >
            {collapsed ? 'S' : 'SAPO'}
          </Title>
        </div>

        {/* Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getCurrentMenuKey()}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          onSelect={({ key }) => {
            // Handle menu item selection
            const findMenuItem = (items, targetKey) => {
              for (const item of items) {
                if (item.key === targetKey) return item;
                if (item.children) {
                  const found = findMenuItem(item.children, targetKey);
                  if (found) return found;
                }
              }
              return null;
            };

            const selectedItem = findMenuItem(rawMenuItems, key);
            if (selectedItem && selectedItem.path) {
              navigate(selectedItem.path);
            }
          }}
          items={menuItems}
          className="sidebar-menu"
        />
      </Sider>

      {/* Main Layout */}
      <AntLayout className="main-content" style={{ marginLeft: collapsed ? 80 : 200 }}>
        {/* Header - Fixed */}
        <Header
          className="fixed-header"
          style={{
            padding: '0 24px',
            background: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0',
            left: collapsed ? 80 : 200,
            width: `calc(100% - ${collapsed ? 80 : 200}px)`,
          }}
        >
          {/* Left side */}
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="sidebar-toggle"
            />
          </div>

          {/* Right side */}
          <div className="header-right">
            {/* Notifications */}
            <Badge count={0} showZero={false}>
              <Button
                type="text"
                icon={<BellOutlined />}
                className="notification-badge"
              />
            </Badge>

            {/* User dropdown */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="user-dropdown">
                <Space>
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                  <div className="user-info">
                    <Text className="user-name">
                      {user?.ho_ten}
                    </Text>
                    <Text type="secondary" className="user-role">
                      {user?.loai_nguoi_dung === 'admin' ? 'Quản trị viên' :
                       user?.loai_nguoi_dung === 'nhan_vien' ? 'Nhân viên' : 'Khách hàng'}
                    </Text>
                  </div>
                </Space>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* Content */}
        <Content className="content-background">
          {/* Breadcrumb */}
          <div className="breadcrumb-container">
            <Breadcrumb
              items={getBreadcrumbItems()}
            />
          </div>

          {/* Page Content */}
          <div className="page-content">
            <Outlet />
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
