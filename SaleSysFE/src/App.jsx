import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { ConfigProvider } from "antd";
import viVN from "antd/locale/vi_VN";
import dayjs from "dayjs";
import "dayjs/locale/vi";

// Import contexts
import { AuthProvider } from "./contexts/AuthContext";
import { PermissionProvider } from "./contexts/PermissionContext";

// Import components
import Layout from "./components/Layout/Layout";
import Login from "./pages/Auth/Login";
import Dashboard from "./pages/Dashboard/Dashboard";
import ProtectedRoute from "./components/ProtectedRoute/ProtectedRoute";

// Import pages
import Users from "./pages/Users/<USER>";
import Roles from "./pages/Roles/Roles";
import Permissions from "./pages/Permissions/Permissions";
import Products from "./pages/Products/Products";
import ProductList from "./pages/Products/ProductList";
import CreateProduct from "./pages/Products/CreateProduct";
import ProductDetail from "./pages/Products/ProductDetail";
import VariantDetail from "./pages/Products/VariantDetail";
import EditProduct from "./pages/Products/EditProduct";
import Categories from "./pages/Products/Categories";
import Brands from "./pages/Products/Brands";
import Orders from "./pages/Orders/Orders";
import OrdersList from "./pages/Orders/OrdersList";
import CreateOrder from "./pages/Orders/CreateOrder";
import CreateOrderDemo from "./pages/Orders/CreateOrderDemo";
import Customers from "./pages/Customers/Customers";
import CustomerGroups from "./pages/Customers/CustomerGroups";
import CustomerDetail from "./pages/Customers/CustomerDetail";
import CongTacVien from "./pages/Customers/CongTacVien";
import Warehouses from "./pages/Warehouses/Warehouses";
import AddWarehouse from "./pages/Warehouses/AddWarehouse";
import EditWarehouse from "./pages/Warehouses/EditWarehouse";
import ViewWarehouse from "./pages/Warehouses/ViewWarehouse";
import Inventory from "./pages/Warehouses/Inventory";
import StockMovements from "./pages/Warehouses/StockMovements";
import StockCheck from "./pages/Warehouses/StockCheck";
import CreateStockCheck from "./pages/Warehouses/CreateStockCheck";
import StockCheckEdit from "./pages/Warehouses/StockCheckEdit";
import Reports from "./pages/Reports/Reports";
import FilterDemo from "./pages/FilterDemo";

// Configure dayjs
dayjs.locale("vi");

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Ant Design theme configuration
const theme = {
  token: {
    colorPrimary: "#1890ff",
    borderRadius: 6,
    fontSize: 14,
  },
  components: {
    Layout: {
      siderBg: "#001529",
      triggerBg: "#002140",
    },
  },
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider locale={viVN} theme={theme}>
        <AuthProvider>
          <PermissionProvider>
            <Router>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />

                {/* Protected routes */}
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<Dashboard />} />

                  {/* User Management */}
                  <Route
                    path="users"
                    element={
                      <ProtectedRoute requiredPermission="XEM_NGUOI_DUNG">
                        <Users />
                      </ProtectedRoute>
                    }
                  />

                  {/* Role Management */}
                  <Route
                    path="roles"
                    element={
                      <ProtectedRoute requiredPermission="XEM_VAI_TRO">
                        <Roles />
                      </ProtectedRoute>
                    }
                  />

                  {/* Permission Management */}
                  <Route
                    path="permissions"
                    element={
                      <ProtectedRoute requiredPermission="XEM_QUYEN">
                        <Permissions />
                      </ProtectedRoute>
                    }
                  />

                  {/* Product Management */}
                  <Route
                    path="products"
                    element={
                      <ProtectedRoute requiredPermission="XEM_SAN_PHAM">
                        <ProductList />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/create"
                    element={
                      <ProtectedRoute requiredPermission="THEM_SAN_PHAM">
                        <CreateProduct />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/detail/:id"
                    element={
                      <ProtectedRoute requiredPermission="XEM_SAN_PHAM">
                        <ProductDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/:productId/variants/:variantId"
                    element={
                      <ProtectedRoute requiredPermission="XEM_SAN_PHAM">
                        <VariantDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/edit/:id"
                    element={
                      <ProtectedRoute requiredPermission="SUA_SAN_PHAM">
                        <EditProduct />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/categories"
                    element={
                      <ProtectedRoute requiredPermission="XEM_LOAI_SAN_PHAM">
                        <Categories />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="products/brands"
                    element={
                      <ProtectedRoute requiredPermission="XEM_NHAN_HIEU">
                        <Brands />
                      </ProtectedRoute>
                    }
                  />

                  {/* Order Management */}
                  <Route
                    path="orders"
                    element={
                      <ProtectedRoute requiredPermission="XEM_DON_HANG">
                        <OrdersList />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="orders/create"
                    element={
                      <ProtectedRoute requiredPermission="THEM_DON_HANG">
                        <CreateOrder />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="orders/demo"
                    element={<CreateOrderDemo />}
                  />

                  {/* Customer Management */}
                  <Route
                    path="customers"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KHACH_HANG">
                        <Customers />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="customers/cong-tac-vien"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KHACH_HANG">
                        <CongTacVien />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="customers/groups"
                    element={
                      <ProtectedRoute requiredPermission="XEM_NHOM_KHACH_HANG">
                        <CustomerGroups />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="customers/:id"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KHACH_HANG">
                        <CustomerDetail />
                      </ProtectedRoute>
                    }
                  />
                  {/* Warehouse Management */}
                  <Route
                    path="warehouses"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KHO_HANG">
                        <Warehouses />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/add"
                    element={
                      <ProtectedRoute requiredPermission="THEM_KHO_HANG">
                        <AddWarehouse />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/edit/:id"
                    element={
                      <ProtectedRoute requiredPermission="SUA_KHO_HANG">
                        <EditWarehouse />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/view/:id"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KHO_HANG">
                        <ViewWarehouse />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/inventory"
                    element={
                      <ProtectedRoute requiredPermission="XEM_TON_KHO">
                        <Inventory />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/movements"
                    element={
                      <ProtectedRoute requiredPermission="XEM_XUAT_NHAP_KHO">
                        <StockMovements />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/check"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KIEM_KE_KHO">
                        <StockCheck />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/check/create"
                    element={
                      <ProtectedRoute requiredPermission="THEM_KIEM_KE_KHO">
                        <CreateStockCheck />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="warehouses/check/:id"
                    element={
                      <ProtectedRoute requiredPermission="XEM_KIEM_KE_KHO">
                        <StockCheckEdit />
                      </ProtectedRoute>
                    }
                  />

                  {/* Reports */}
                  <Route
                    path="reports"
                    element={
                      <ProtectedRoute requiredPermission="XEM_BAO_CAO">
                        <Reports />
                      </ProtectedRoute>
                    }
                  />

                  {/* Filter Demo */}
                  <Route
                    path="filter-demo"
                    element={<FilterDemo />}
                  />
                </Route>

                {/* Catch all route */}
                <Route
                  path="*"
                  element={<Navigate to="/dashboard" replace />}
                />
              </Routes>
            </Router>
          </PermissionProvider>
        </AuthProvider>
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
