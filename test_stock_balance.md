# Test Case: Cân bằng kho sau kiểm kê

## 🧪 Mục tiêu test:
<PERSON><PERSON><PERSON> bảo khi cân bằng kho sau kiểm kê, tồn kho được cập nhật đúng và hiển thị ngay lập tức.

## 📋 Các bước test:

### **Bước 1: <PERSON><PERSON><PERSON> tra tồn kho ban đầu**
1. Vào: http://localhost:5174/warehouses/inventory
2. Ghi nhận số lượng tồn kho của một sản phẩm cụ thể
3. Ví dụ: Sản phẩm A có 100 cái

### **Bước 2: Tạo phiếu kiểm kê**
1. Vào: http://localhost:5174/warehouses/check/create
2. Chọn kho (đã auto-select)
3. Thêm sản phẩm A vào phiếu kiểm kê
4. <PERSON><PERSON><PERSON> <PERSON>hi<PERSON> → chuy<PERSON><PERSON> sang trạng thái "<PERSON><PERSON> kiểm kho"

### **Bước 3: <PERSON><PERSON><PERSON> k<PERSON> sản phẩm**
1. <PERSON><PERSON><PERSON> chi tiết phiếu kiểm kê
2. <PERSON><PERSON> "Kiể<PERSON> kê" cho sản phẩm A
3. Nhập số lượng thực tế khác với hệ thống (ví dụ: 95 thay vì 100)
4. Lưu → thấy chênh lệch -5

### **Bước 4: Cân bằng kho**
1. Khi đã kiểm kê xong tất cả sản phẩm (100% progress)
2. Click "Cân bằng kho"
3. Xác nhận → thấy message "Cân bằng kho thành công! Tồn kho đã được cập nhật."

### **Bước 5: Kiểm tra kết quả**
1. Vào lại: http://localhost:5174/warehouses/inventory
2. **✅ EXPECTED**: Sản phẩm A bây giờ có 95 cái (đã cập nhật)
3. **✅ EXPECTED**: Không cần refresh trang, dữ liệu tự động cập nhật

## 🔧 Những gì đã sửa:

### **Frontend Hook Updates:**
```javascript
// useCompleteStockCheck - Thêm invalidate inventory cache
onSuccess: (data, id) => {
  message.success('Cân bằng kho thành công! Tồn kho đã được cập nhật.');
  queryClient.invalidateQueries(['stockCheckDetails', id]);
  queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
  // ✅ QUAN TRỌNG: Invalidate inventory cache để cập nhật tồn kho
  queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
  // Invalidate stock movements để cập nhật lịch sử
  queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.movements());
}

// useUpdateStockCheckItem - Thêm invalidate inventory cache
onSuccess: (data, variables) => {
  message.success('Cập nhật số lượng thành công!');
  queryClient.invalidateQueries(['stockCheckDetails', variables.stockCheckId]);
  queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
  // Cập nhật inventory cache để hiển thị số liệu mới nhất
  queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
}
```

### **Backend Logic (đã có sẵn):**
```javascript
// completeStockCheck - Cập nhật tồn kho thực tế
await inventory.update({
  so_luong_ton: newStock,  // Số lượng thực tế từ kiểm kê
  ngay_cap_nhap: new Date()
}, { transaction });

// Tạo lịch sử kho
await LichSuKho.create({
  loai_giao_dich: changeAmount > 0 ? 'kiem_ke_tang' : 'kiem_ke_giam',
  so_luong_truoc: oldStock,
  so_luong_thay_doi: changeAmount,
  so_luong_sau: newStock,
  ly_do: 'Điều chỉnh sau kiểm kê'
}, { transaction });
```

## 🎯 Kết quả mong đợi:
- ✅ Tồn kho được cập nhật ngay lập tức
- ✅ Không cần refresh trang
- ✅ Lịch sử kho được ghi nhận
- ✅ Cache được invalidate đúng cách
- ✅ UI hiển thị số liệu mới nhất

## 🚨 Nếu vẫn không hoạt động:
1. Kiểm tra console có lỗi API không
2. Kiểm tra Network tab xem API có được gọi không
3. Kiểm tra database xem tồn kho có được cập nhật không
4. Kiểm tra React Query DevTools xem cache có được invalidate không
