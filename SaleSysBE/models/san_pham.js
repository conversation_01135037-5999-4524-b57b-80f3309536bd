'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SanPham extends Model {
    static associate(models) {
      // Quan hệ với loại sản phẩm
      SanPham.belongsTo(models.LoaiSanPham, {
        foreignKey: 'loai_san_pham_id',
        as: 'loaiSanPham'
      });

      // Quan hệ với nhãn hiệu
      <PERSON>.belongsTo(models.NhanHieu, {
        foreignKey: 'nhan_hieu_id',
        as: 'nhan<PERSON><PERSON>'
      });

      // Quan hệ với tag
      SanPham.belongsTo(models.Tag, {
        foreignKey: 'tag_id',
        as: 'tag'
      });

      // Quan hệ với thuộc tính (n-n)
      SanPham.belongsToMany(models.ThuocTinh, {
        through: models.ThuocTinhSanPham,
        foreignKey: 'san_pham_id',
        otherKey: 'thuoc_tinh_id',
        as: 'thuocTinhList'
      });

      // Quan hệ với phiên bản sản phẩm (1-n)
      SanPham.hasMany(models.PhienBanSan<PERSON>ham, {
        foreignKey: 'san_pham_id',
        as: 'phienBanList'
      });

      // Quan hệ với ảnh sản phẩm (1-n)
      SanPham.hasMany(models.AnhSanPham, {
        foreignKey: 'san_pham_id',
        as: 'anhList'
      });
    }
  }

  SanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    loai_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'loai_san_pham',
        key: 'id'
      }
    },
    ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    nhan_hieu_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'nhan_hieu',
        key: 'id'
      }
    },
    tag_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'tag',
        key: 'id'
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'SanPham',
    tableName: 'san_pham',
    timestamps: true,
    createdAt: 'ngay_tao',
    updatedAt: 'ngay_cap_nhap'
  });

  return SanPham;
};
