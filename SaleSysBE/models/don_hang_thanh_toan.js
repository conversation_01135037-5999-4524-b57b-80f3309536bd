'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangThanhToan extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      DonHangThanhToan.belongsTo(models.<PERSON>Hang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });
    }
  }

  DonHangThanhToan.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    phuong_thuc: {
      type: DataTypes.ENUM('Tien_mat', 'Chuyen_khoan', 'VNPay', 'MoMo', 'ZaloPay', 'The_tin_dung'),
      allowNull: false,
      comment: 'Tien_mat, <PERSON>yen_khoan, VNPay, ...'
    },
    so_tien: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      validate: {
        min: 0
      }
    },
    ngay_thanh_toan: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    trang_thai: {
      type: DataTypes.ENUM('da_thanh_toan', 'cho_thanh_toan', 'that_bai'),
      defaultValue: 'da_thanh_toan'
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHangThanhToan',
    tableName: 'don_hang_thanh_toan',
    timestamps: false
  });

  return DonHangThanhToan;
};
