'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Tag extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm (1-n)
      Tag.hasMany(models.SanPham, {
        foreignKey: 'tag_id',
        as: 'sanPhamList'
      });

      // Quan hệ với người dùng (n-n)
      Tag.belongsToMany(models.NguoiDung, {
        through: models.TagNguoiDung,
        foreignKey: 'tag_id',
        otherKey: 'nguoi_dung_id',
        as: 'nguoiDungList'
      });

      // Quan hệ với đơn hàng (n-n)
      Tag.belongsToMany(models.DonHang, {
        through: models.DonHangTag,
        foreignKey: 'tag_id',
        otherKey: 'don_hang_id',
        as: 'donHangList'
      });
    }
  }

  Tag.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 50]
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Tag',
    tableName: 'tag'
  });

  return Tag;
};
