'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangSanPham extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      DonHangSanPham.belongsTo(models.<PERSON>Hang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });

      // Quan hệ với phiên bản sản phẩm
      DonHangSanPham.belongsTo(models.PhienBanSanPham, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'phienBanSanPham'
      });
    }
  }

  DonHangSanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    phien_ban_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phien_ban_san_pham',
        key: 'id'
      }
    },
    ten_san_pham: {
      type: DataTypes.STRING,
      allowNull: false
    },
    so_luong: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    don_gia: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      validate: {
        min: 0
      }
    },
    chiet_khau: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    thanh_tien: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHangSanPham',
    tableName: 'don_hang_san_pham',
    timestamps: false
  });

  return DonHangSanPham;
};
