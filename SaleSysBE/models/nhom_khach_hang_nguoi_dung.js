'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class NhomKhachHangNguoiDung extends Model {
    static associate(models) {
      // Quan hệ với người dùng
      NhomKhachHangNguoiDung.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });

      // Quan hệ với nhóm khách hàng
      NhomKhachHangNguoiDung.belongsTo(models.NhomKhachHang, {
        foreignKey: 'nhom_khach_hang_id',
        as: 'nhomKhachHang'
      });
    }
  }

  NhomKhachHangNguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    nhom_khach_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nhom_khach_hang',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'NhomKhachHangNguoiDung',
    tableName: 'nhom_khach_hang_nguoi_dung',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['nguoi_dung_id', 'nhom_khach_hang_id'],
        name: 'nhom_kh_nd_unique'
      }
    ]
  });

  return NhomKhachHangNguoiDung;
};
