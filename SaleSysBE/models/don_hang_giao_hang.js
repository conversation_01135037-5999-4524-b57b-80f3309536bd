'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangGiaoHang extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      Don<PERSON>angGiaoHang.belongsTo(models.<PERSON>Hang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });
    }
  }

  DonHangGiaoHang.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    ma_dong_goi: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    hinh_thuc_giao_hang: {
      type: DataTypes.ENUM('Nhan_tai_cua_hang', 'Giao_tan_nha', 'Vc_noi_bo', 'Giao_hang_nhanh', 'Viettel_Post'),
      allowNull: true,
      comment: 'Nhan_tai_cua_hang, Giao_tan_nha, Vc_noi_bo, ...'
    },
    trang_thai: {
      type: DataTypes.ENUM('cho_giao', 'dang_giao', 'da_giao', 'that_bai'),
      defaultValue: 'cho_giao'
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW
    },
    ngay_giao: {
      type: DataTypes.DATE,
      allowNull: true
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DonHangGiaoHang',
    tableName: 'don_hang_giao_hang',
    timestamps: false
  });

  return DonHangGiaoHang;
};
