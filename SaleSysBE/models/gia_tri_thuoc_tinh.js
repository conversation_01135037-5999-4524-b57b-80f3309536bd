'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class GiaTriThuocTinh extends Model {
    static associate(models) {
      // Quan hệ với thuộc tính
      GiaTriThuocTinh.belongsTo(models.ThuocTinh, {
        foreignKey: 'thuoc_tinh_id',
        as: 'thuocTinh'
      });

      // Quan hệ với phiên bản sản phẩm (n-n)
      GiaTriThuocTinh.belongsToMany(models.PhienBanSanPham, {
        through: models.PhienBanGiaTriThuocTinh,
        foreignKey: 'gia_tri_thuoc_tinh_id',
        otherKey: 'phien_ban_san_pham_id',
        as: 'phienBanSanPhamList'
      });
    }
  }

  GiaTriThuocTinh.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    thuoc_tinh_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'thuoc_tinh',
        key: 'id'
      }
    },
    gia_tri: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'GiaTriThuocTinh',
    tableName: 'gia_tri_thuoc_tinh'
  });

  return GiaTriThuocTinh;
};
