'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Goi<PERSON>BanHang extends Model {
    static associate(models) {
      // Quan hệ với người dùng (1-1)
      GoiYBanHang.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });
    }
  }

  GoiYBanHang.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    chinh_sach_gia_mac_dinh: {
      type: DataTypes.STRING,
      allowNull: true
    },
    chiet_khau_mac_dinh: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    hinh_thuc_thanh_toan_mac_dinh: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'GoiYBanHang',
    tableName: 'goi_y_ban_hang',
    timestamps: false
  });

  return GoiYBanHang;
};
