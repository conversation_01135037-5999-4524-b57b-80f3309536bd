'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TagNguoiDung extends Model {
    static associate(models) {
      // Quan hệ với người dùng
      TagNguoiDung.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });

      // Quan hệ với tag
      TagNguoiDung.belongsTo(models.Tag, {
        foreignKey: 'tag_id',
        as: 'tag'
      });
    }
  }

  TagNguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    tag_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'tag',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'TagNguoiDung',
    tableName: 'tag_nguoi_dung',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['nguoi_dung_id', 'tag_id'],
        name: 'tag_nd_unique'
      }
    ]
  });

  return TagNguoiDung;
};
