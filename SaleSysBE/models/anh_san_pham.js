'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class AnhSanPham extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm
      AnhSanPham.belongsTo(models.SanPham, {
        foreignKey: 'san_pham_id',
        as: 'sanPham'
      });
    }
  }

  AnhSanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL gốc từ Cloudinary'
    },
    public_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Public ID từ Cloudinary'
    },
    thumbnail_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL thumbnail từ Cloudinary'
    },
    optimized_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL tối ưu từ Cloudinary'
    },
    san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'san_pham',
        key: 'id'
      }
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'AnhSanPham',
    tableName: 'anh_san_pham',
    timestamps: true,
  });

  return AnhSanPham;
};
