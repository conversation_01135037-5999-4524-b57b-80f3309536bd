'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PhienBanSanPham extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm
      PhienBanSanPham.belongsTo(models.SanPham, {
        foreignKey: 'san_pham_id',
        as: 'sanPham'
      });

      // Quan hệ với giá trị thuộc tính (n-n)
      PhienBanSanPham.belongsToMany(models.GiaTriThuocTinh, {
        through: models.PhienBanGiaTriThuocTinh,
        foreignKey: 'phien_ban_san_pham_id',
        otherKey: 'gia_tri_thuoc_tinh_id',
        as: 'giaTriThuocTinhList'
      });

      // Quan hệ với tồn kho (1-n)
      PhienBanSanPham.hasMany(models.TonKhoPhienBan, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'tonKhoList'
      });

      // <PERSON>uan hệ với lịch sử kho (1-n)
      PhienBanSanPham.hasMany(models.LichSuKho, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'lichSuKhoList'
      });

      // Quan hệ với đơn hàng sản phẩm (1-n)
      PhienBanSanPham.hasMany(models.DonHangSanPham, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'donHangSanPhamList'
      });

      // Quan hệ với chi tiết kiểm kê (1-n)
      if (models.ChiTietKiemKe) {
        PhienBanSanPham.hasMany(models.ChiTietKiemKe, {
          foreignKey: 'phien_ban_san_pham_id',
          as: 'chiTietKiemKeList'
        });
      }
    }
  }

  PhienBanSanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'san_pham',
        key: 'id'
      }
    },
    ten_phien_ban: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ma: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      comment: 'Mã sản phẩm không trùng lặp, để trống thì tự động sinh theo quy tắc'
    },
    ma_vach: {
      type: DataTypes.STRING,
      allowNull: true
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    khoi_luong: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    don_vi_tinh: {
      type: DataTypes.STRING,
      allowNull: true
    },
    gia_le: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    gia_buon: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    gia_nhap: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    trang_thai: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: '1: active, 0: inactive'
    },
    anh_phien_ban: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'PhienBanSanPham',
    tableName: 'phien_ban_san_pham',
    timestamps: true,
    createdAt: 'ngay_tao',
    updatedAt: 'ngay_cap_nhap',
    hooks: {
      beforeCreate: async (phienBan) => {
        if (!phienBan.ma) {
          // Tự động sinh mã sản phẩm
          const count = await PhienBanSanPham.count();
          phienBan.ma = `SP${String(count + 1).padStart(6, '0')}`;
        }
      }
    }
  });

  return PhienBanSanPham;
};
