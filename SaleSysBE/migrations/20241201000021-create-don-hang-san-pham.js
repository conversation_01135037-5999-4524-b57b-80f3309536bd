'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_san_pham', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      phien_ban_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phien_ban_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      ten_san_pham: {
        type: Sequelize.STRING,
        allowNull: false
      },
      so_luong: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1
        }
      },
      don_gia: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        validate: {
          min: 0
        }
      },
      chiet_khau: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      thanh_tien: {
        type: Sequelize.DOUBLE,
        allowNull: false
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_san_pham', ['don_hang_id']);
    await queryInterface.addIndex('don_hang_san_pham', ['phien_ban_san_pham_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_san_pham');
  }
};
