'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('cong_no_nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      tong_cong_no: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('cong_no_nguoi_dung', ['nguoi_dung_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('cong_no_nguoi_dung');
  }
};
