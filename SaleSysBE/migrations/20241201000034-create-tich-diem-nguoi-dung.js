'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tich_diem_nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      diem_hien_tai: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      hang_tich_diem: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_het_han_the: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      gia_tri_con_lai_len_hang: {
        type: Sequelize.DOUBLE,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('tich_diem_nguoi_dung', ['nguoi_dung_id']);
    await queryInterface.addIndex('tich_diem_nguoi_dung', ['hang_tich_diem']);
    await queryInterface.addIndex('tich_diem_nguoi_dung', ['diem_hien_tai']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tich_diem_nguoi_dung');
  }
};
