'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('chi_tiet_kiem_ke', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      phieu_kiem_ke_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phieu_kiem_ke',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID phiếu kiểm kê'
      },
      phien_ban_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phien_ban_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID phiên bản sản phẩm'
      },
      so_luong_he_thong: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '<PERSON><PERSON> lượng theo hệ thống'
      },
      so_luong_thuc_te: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Số lượng thực tế kiểm được'
      },
      so_luong_lech: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số lượng chênh lệch (thực tế - hệ thống)'
      },
      gia_von: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Giá vốn tại thời điểm kiểm kê'
      },
      gia_tri_lech: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Giá trị chênh lệch (số lượng lệch * giá vốn)'
      },
      trang_thai: {
        type: Sequelize.ENUM('chua_kiem', 'da_kiem', 'co_lech'),
        allowNull: false,
        defaultValue: 'chua_kiem',
        comment: 'Trạng thái kiểm kê chi tiết'
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Ghi chú cho sản phẩm này'
      },
      nguoi_kiem: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Người kiểm sản phẩm này'
      },
      ngay_kiem: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Ngày kiểm sản phẩm này'
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('chi_tiet_kiem_ke', ['phieu_kiem_ke_id']);
    await queryInterface.addIndex('chi_tiet_kiem_ke', ['phien_ban_san_pham_id']);
    await queryInterface.addIndex('chi_tiet_kiem_ke', ['trang_thai']);

    // Add unique constraint for combination
    await queryInterface.addIndex('chi_tiet_kiem_ke', ['phieu_kiem_ke_id', 'phien_ban_san_pham_id'], {
      unique: true,
      name: 'unique_phieu_kiem_ke_phien_ban'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('chi_tiet_kiem_ke');
  }
};
