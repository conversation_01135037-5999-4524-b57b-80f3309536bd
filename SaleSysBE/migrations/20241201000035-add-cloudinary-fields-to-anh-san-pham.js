'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if columns exist before adding
    const tableDescription = await queryInterface.describeTable('anh_san_pham');
    
    // Add public_id column if it doesn't exist
    if (!tableDescription.public_id) {
      await queryInterface.addColumn('anh_san_pham', 'public_id', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Public ID từ Cloudinary'
      });
    }

    // Add thumbnail_url column if it doesn't exist
    if (!tableDescription.thumbnail_url) {
      await queryInterface.addColumn('anh_san_pham', 'thumbnail_url', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'URL thumbnail từ Cloudinary'
      });
    }

    // Add optimized_url column if it doesn't exist
    if (!tableDescription.optimized_url) {
      await queryInterface.addColumn('anh_san_pham', 'optimized_url', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'URL tối ưu từ Cloudinary'
      });
    }

    // Update url column comment
    await queryInterface.changeColumn('anh_san_pham', 'url', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'URL gốc từ Cloudinary'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('anh_san_pham', 'public_id');
    await queryInterface.removeColumn('anh_san_pham', 'thumbnail_url');
    await queryInterface.removeColumn('anh_san_pham', 'optimized_url');
    
    // Revert url column comment
    await queryInterface.changeColumn('anh_san_pham', 'url', {
      type: Sequelize.STRING,
      allowNull: true
    });
  }
};
