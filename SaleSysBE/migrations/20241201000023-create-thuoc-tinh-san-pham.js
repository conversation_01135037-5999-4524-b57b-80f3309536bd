'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('thuoc_tinh_san_pham', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      thuoc_tinh_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'thuoc_tinh',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('thuoc_tinh_san_pham', ['san_pham_id']);
    await queryInterface.addIndex('thuoc_tinh_san_pham', ['thuoc_tinh_id']);
    
    // Add unique constraint for combination
    await queryInterface.addIndex('thuoc_tinh_san_pham', ['san_pham_id', 'thuoc_tinh_id'], {
      unique: true,
      name: 'unique_san_pham_thuoc_tinh'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('thuoc_tinh_san_pham');
  }
};
