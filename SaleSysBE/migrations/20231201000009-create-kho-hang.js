'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('kho_hang', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ten_kho: {
        type: Sequelize.STRING,
        allowNull: false
      },
      dia_chi: {
        type: Sequelize.STRING,
        allowNull: true
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      trang_thai: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '1: active, 0: inactive'
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('kho_hang');
  }
};
