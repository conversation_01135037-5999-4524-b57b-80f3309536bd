const express = require('express');
const { <PERSON>uy<PERSON>, <PERSON><PERSON><PERSON><PERSON> } = require('../models');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/permissions
 * @desc Lấy danh sách quyền
 * @access Private (Cần quyền XEM_QUYEN)
 */
router.get('/', requirePermission('XEM_QUYEN'), asyncHandler(async (req, res) => {
  const permissions = await Quyen.findAll({
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        through: { attributes: [] },
        attributes: ['id', 'ma_vai_tro', 'ten_vai_tro']
      }
    ],
    order: [['ma_quyen', 'ASC']]
  });

  res.json({
    success: true,
    data: { permissions }
  });
}));

/**
 * @route GET /api/permissions/:id
 * @desc Lấy thông tin chi tiết quyền
 * @access Private (Cần quyền XEM_QUYEN)
 */
router.get('/:id', requirePermission('XEM_QUYEN'), asyncHandler(async (req, res) => {
  const permission = await Quyen.findByPk(req.params.id, {
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        through: { attributes: [] }
      }
    ]
  });

  if (!permission) {
    throw new AppError('Không tìm thấy quyền', 404);
  }

  res.json({
    success: true,
    data: { permission }
  });
}));

/**
 * @route POST /api/permissions
 * @desc Tạo quyền mới
 * @access Private (Admin only)
 */
router.post('/', requireAdmin, asyncHandler(async (req, res) => {
  const { ma_quyen, ten_quyen, mo_ta } = req.body;

  // Validation
  if (!ma_quyen || !ten_quyen) {
    throw new AppError('Mã quyền và tên quyền là bắt buộc', 400);
  }

  // Kiểm tra mã quyền đã tồn tại
  const existingPermission = await Quyen.findOne({ where: { ma_quyen } });
  if (existingPermission) {
    throw new AppError('Mã quyền đã tồn tại', 400);
  }

  // Tạo quyền mới
  const newPermission = await Quyen.create({
    ma_quyen: ma_quyen.toUpperCase(),
    ten_quyen,
    mo_ta
  });

  res.status(201).json({
    success: true,
    message: 'Tạo quyền thành công',
    data: { permission: newPermission }
  });
}));

/**
 * @route PUT /api/permissions/:id
 * @desc Cập nhật quyền
 * @access Private (Admin only)
 */
router.put('/:id', requireAdmin, asyncHandler(async (req, res) => {
  const { ma_quyen, ten_quyen, mo_ta } = req.body;

  const permission = await Quyen.findByPk(req.params.id);
  if (!permission) {
    throw new AppError('Không tìm thấy quyền', 404);
  }

  // Kiểm tra mã quyền đã tồn tại (nếu thay đổi)
  if (ma_quyen && ma_quyen !== permission.ma_quyen) {
    const existingPermission = await Quyen.findOne({ where: { ma_quyen } });
    if (existingPermission) {
      throw new AppError('Mã quyền đã tồn tại', 400);
    }
  }

  // Cập nhật thông tin quyền
  await permission.update({
    ma_quyen: ma_quyen ? ma_quyen.toUpperCase() : permission.ma_quyen,
    ten_quyen: ten_quyen || permission.ten_quyen,
    mo_ta: mo_ta !== undefined ? mo_ta : permission.mo_ta
  });

  res.json({
    success: true,
    message: 'Cập nhật quyền thành công',
    data: { permission }
  });
}));

/**
 * @route DELETE /api/permissions/:id
 * @desc Xóa quyền
 * @access Private (Admin only)
 */
router.delete('/:id', requireAdmin, asyncHandler(async (req, res) => {
  const permission = await Quyen.findByPk(req.params.id);
  if (!permission) {
    throw new AppError('Không tìm thấy quyền', 404);
  }

  // Kiểm tra xem quyền có đang được sử dụng không
  const rolesWithPermission = await VaiTro.findAll({
    include: [
      {
        model: Quyen,
        as: 'quyenList',
        where: { id: req.params.id }
      }
    ]
  });

  if (rolesWithPermission.length > 0) {
    throw new AppError('Không thể xóa quyền đang được sử dụng', 400);
  }

  await permission.destroy();

  res.json({
    success: true,
    message: 'Xóa quyền thành công'
  });
}));

module.exports = router;
