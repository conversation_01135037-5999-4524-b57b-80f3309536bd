const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getBrands,
  createBrand,
  updateBrand,
  deleteBrand,
  getTags,
  getAttributes
} = require('../controllers/productController');

const router = express.Router();



// Product related data routes (public for now) - MUST BE BEFORE /:id route
router.get('/categories', requirePermission('XEM_LOAI_SAN_PHAM'), asyncHandler(getCategories));
router.post('/categories', requirePermission('THEM_LOAI_SAN_PHAM'), asyncHandler(createCategory));
router.put('/categories/:id', requirePermission('SUA_LOAI_SAN_PHAM'), asyncHandler(updateCategory));
router.delete('/categories/:id', requirePermission('XOA_LOAI_SAN_PHAM'), asyncHandler(deleteCategory));

router.get('/brands', requirePermission('XEM_NHAN_HIEU'), asyncHandler(getBrands));
router.post('/brands', requirePermission('THEM_NHAN_HIEU'), asyncHandler(createBrand));
router.put('/brands/:id', requirePermission('SUA_NHAN_HIEU'), asyncHandler(updateBrand));
router.delete('/brands/:id', requirePermission('XOA_NHAN_HIEU'), asyncHandler(deleteBrand));

router.get('/tags', requirePermission('XEM_SAN_PHAM'), asyncHandler(getTags));
router.get('/attributes', requirePermission('XEM_SAN_PHAM'), asyncHandler(getAttributes));

/**
 * @route GET /api/products
 * @desc Lấy danh sách sản phẩm
 * @access Private (Cần quyền XEM_SAN_PHAM)
 */
router.get('/', requirePermission('XEM_SAN_PHAM'), asyncHandler(getProducts));

// Test route without auth - bypass all middleware
router.get('/test', asyncHandler(async (req, res) => {
  // Temporarily bypass auth by setting a fake user
  req.user = { username: 'test' };
  return getProducts(req, res);
}));

/**
 * @route GET /api/products/:id
 * @desc Lấy thông tin chi tiết sản phẩm
 * @access Private (Cần quyền XEM_SAN_PHAM)
 */
router.get('/:id', requirePermission('XEM_SAN_PHAM'), asyncHandler(getProduct));

/**
 * @route POST /api/products
 * @desc Tạo sản phẩm mới
 * @access Private (Cần quyền THEM_SAN_PHAM)
 */
router.post('/', requirePermission('THEM_SAN_PHAM'), asyncHandler(createProduct));

/**
 * @route PUT /api/products/:id
 * @desc Cập nhật sản phẩm
 * @access Private (Cần quyền SUA_SAN_PHAM)
 */
router.put('/:id', requirePermission('SUA_SAN_PHAM'), asyncHandler(updateProduct));

/**
 * @route DELETE /api/products/:id
 * @desc Xóa sản phẩm
 * @access Private (Cần quyền XOA_SAN_PHAM)
 */
router.delete('/:id', requirePermission('XOA_SAN_PHAM'), asyncHandler(deleteProduct));

module.exports = router;
