const express = require('express');
const { Op } = require('sequelize');
const { NguoiDung, VaiTro, Quyen } = require('../models');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/users
 * @desc Lấy danh sách người dùng
 * @access Private (Cần quyền XEM_NGUOI_DUNG)
 */
router.get('/', requirePermission('XEM_NGUOI_DUNG'), asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '', 
    loai_nguoi_dung = '', 
    trang_thai = '' 
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = {};

  // Tìm kiếm theo tên hoặc email
  if (search) {
    whereClause[Op.or] = [
      { ho_ten: { [Op.like]: `%${search}%` } },
      { email: { [Op.like]: `%${search}%` } },
      { so_dien_thoai: { [Op.like]: `%${search}%` } }
    ];
  }

  // Lọc theo loại người dùng
  if (loai_nguoi_dung) {
    whereClause.loai_nguoi_dung = loai_nguoi_dung;
  }

  // Lọc theo trạng thái
  if (trang_thai) {
    whereClause.trang_thai = trang_thai;
  }

  const { count, rows } = await NguoiDung.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        through: { attributes: [] }
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_tao', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      users: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    }
  });
}));

/**
 * @route GET /api/users/:id
 * @desc Lấy thông tin chi tiết người dùng
 * @access Private (Cần quyền XEM_NGUOI_DUNG)
 */
router.get('/:id', requirePermission('XEM_NGUOI_DUNG'), asyncHandler(async (req, res) => {
  const user = await NguoiDung.findByPk(req.params.id, {
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        include: [
          {
            model: Quyen,
            as: 'quyenList'
          }
        ]
      }
    ]
  });

  if (!user) {
    throw new AppError('Không tìm thấy người dùng', 404);
  }

  res.json({
    success: true,
    data: { user }
  });
}));

/**
 * @route POST /api/users
 * @desc Tạo người dùng mới
 * @access Private (Cần quyền THEM_NGUOI_DUNG)
 */
router.post('/', requirePermission('THEM_NGUOI_DUNG'), asyncHandler(async (req, res) => {
  const { ho_ten, email, mat_khau, so_dien_thoai, loai_nguoi_dung, vai_tro_ids = [] } = req.body;

  // Validation
  if (!ho_ten || !email) {
    throw new AppError('Họ tên và email là bắt buộc', 400);
  }

  // Kiểm tra email đã tồn tại
  const existingUser = await NguoiDung.findOne({ where: { email } });
  if (existingUser) {
    throw new AppError('Email đã được sử dụng', 400);
  }

  // Tạo user mới
  const newUser = await NguoiDung.create({
    ho_ten,
    email,
    mat_khau,
    so_dien_thoai,
    loai_nguoi_dung,
    nguoi_tao: req.user.email
  });

  // Gán vai trò cho user
  if (vai_tro_ids.length > 0) {
    const roles = await VaiTro.findAll({
      where: { id: vai_tro_ids }
    });
    await newUser.setVaiTroList(roles);
  }

  // Lấy thông tin user với vai trò
  const userWithRoles = await NguoiDung.findByPk(newUser.id, {
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList'
      }
    ]
  });

  res.status(201).json({
    success: true,
    message: 'Tạo người dùng thành công',
    data: { user: userWithRoles }
  });
}));

/**
 * @route PUT /api/users/:id
 * @desc Cập nhật thông tin người dùng
 * @access Private (Cần quyền SUA_NGUOI_DUNG)
 */
router.put('/:id', requirePermission('SUA_NGUOI_DUNG'), asyncHandler(async (req, res) => {
  const { ho_ten, email, so_dien_thoai, loai_nguoi_dung, trang_thai, vai_tro_ids } = req.body;

  const user = await NguoiDung.findByPk(req.params.id);
  if (!user) {
    throw new AppError('Không tìm thấy người dùng', 404);
  }

  // Kiểm tra email đã tồn tại (nếu thay đổi email)
  if (email && email !== user.email) {
    const existingUser = await NguoiDung.findOne({ where: { email } });
    if (existingUser) {
      throw new AppError('Email đã được sử dụng', 400);
    }
  }

  // Cập nhật thông tin user
  await user.update({
    ho_ten: ho_ten || user.ho_ten,
    email: email || user.email,
    so_dien_thoai: so_dien_thoai || user.so_dien_thoai,
    loai_nguoi_dung: loai_nguoi_dung || user.loai_nguoi_dung,
    trang_thai: trang_thai || user.trang_thai,
    nguoi_cap_nhap: req.user.email
  });

  // Cập nhật vai trò (nếu có)
  if (vai_tro_ids && Array.isArray(vai_tro_ids)) {
    const roles = await VaiTro.findAll({
      where: { id: vai_tro_ids }
    });
    await user.setVaiTroList(roles);
  }

  // Lấy thông tin user đã cập nhật
  const updatedUser = await NguoiDung.findByPk(user.id, {
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList'
      }
    ]
  });

  res.json({
    success: true,
    message: 'Cập nhật người dùng thành công',
    data: { user: updatedUser }
  });
}));

/**
 * @route DELETE /api/users/:id
 * @desc Xóa người dùng
 * @access Private (Cần quyền XOA_NGUOI_DUNG hoặc Admin)
 */
router.delete('/:id', requirePermission('XOA_NGUOI_DUNG'), asyncHandler(async (req, res) => {
  const user = await NguoiDung.findByPk(req.params.id);
  if (!user) {
    throw new AppError('Không tìm thấy người dùng', 404);
  }

  // Không cho phép xóa chính mình
  if (user.id === req.userId) {
    throw new AppError('Không thể xóa chính mình', 400);
  }

  await user.destroy();

  res.json({
    success: true,
    message: 'Xóa người dùng thành công'
  });
}));

/**
 * @route POST /api/users/:id/assign-roles
 * @desc Gán vai trò cho người dùng
 * @access Private (Admin only)
 */
router.post('/:id/assign-roles', requireAdmin, asyncHandler(async (req, res) => {
  const { vai_tro_ids } = req.body;

  if (!Array.isArray(vai_tro_ids)) {
    throw new AppError('vai_tro_ids phải là một mảng', 400);
  }

  const user = await NguoiDung.findByPk(req.params.id);
  if (!user) {
    throw new AppError('Không tìm thấy người dùng', 404);
  }

  const roles = await VaiTro.findAll({
    where: { id: vai_tro_ids }
  });

  await user.setVaiTroList(roles);

  res.json({
    success: true,
    message: 'Gán vai trò thành công'
  });
}));

module.exports = router;
