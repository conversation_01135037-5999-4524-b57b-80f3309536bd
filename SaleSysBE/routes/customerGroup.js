const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const {
  getCustomerGroups,
  createCustomerGroup,
  updateCustomerGroup,
  deleteCustomerGroup,
  getAllCustomerGroupsNoPagin
} = require('../controllers/customerGroupController');

const router = express.Router();

router.get('/', requirePermission('XEM_NHOM_KHACH_HANG'), asyncHandler(getCustomerGroups));
router.get('/all', requirePermission('XEM_NHOM_KHACH_HANG'), asyncHandler(getAllCustomerGroupsNoPagin));

router.post('/', requirePermission('THEM_NHOM_KHACH_HANG'), asyncHandler(createCustomerGroup));

router.put('/:id', requirePermission('SUA_NHOM_KHACH_HANG'), asyncHandler(updateCustomerGroup));

router.delete('/:id', requirePermission('XOA_NHOM_KHACH_HANG'), asyncHandler(deleteCustomerGroup));

module.exports = router;
