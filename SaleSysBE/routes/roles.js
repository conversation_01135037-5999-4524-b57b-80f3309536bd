const express = require('express');
const { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } = require('../models');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/roles
 * @desc Lấy danh sách vai trò
 * @access Private (Cần quyền XEM_VAI_TRO)
 */
router.get('/', requirePermission('XEM_VAI_TRO'), asyncHandler(async (req, res) => {
  const roles = await VaiTro.findAll({
    include: [
      {
        model: Quyen,
        as: 'quyenList',
        through: { attributes: [] }
      }
    ],
    order: [['id', 'ASC']]
  });

  res.json({
    success: true,
    data: { roles }
  });
}));

/**
 * @route GET /api/roles/:id
 * @desc Lấy thông tin chi tiết vai trò
 * @access Private (<PERSON>ần quyền XEM_VAI_TRO)
 */
router.get('/:id', requirePermission('XEM_VAI_TRO'), asyncHandler(async (req, res) => {
  const role = await VaiTro.findByPk(req.params.id, {
    include: [
      {
        model: Quyen,
        as: 'quyenList',
        through: { attributes: [] }
      },
      {
        model: NguoiDung,
        as: 'nguoiDungList',
        through: { attributes: [] },
        attributes: ['id', 'ho_ten', 'email']
      }
    ]
  });

  if (!role) {
    throw new AppError('Không tìm thấy vai trò', 404);
  }

  res.json({
    success: true,
    data: { role }
  });
}));

/**
 * @route POST /api/roles
 * @desc Tạo vai trò mới
 * @access Private (Admin only)
 */
router.post('/', requireAdmin, asyncHandler(async (req, res) => {
  const { ma_vai_tro, ten_vai_tro, mo_ta, quyen_ids = [] } = req.body;

  // Validation
  if (!ma_vai_tro || !ten_vai_tro) {
    throw new AppError('Mã vai trò và tên vai trò là bắt buộc', 400);
  }

  // Kiểm tra mã vai trò đã tồn tại
  const existingRole = await VaiTro.findOne({ where: { ma_vai_tro } });
  if (existingRole) {
    throw new AppError('Mã vai trò đã tồn tại', 400);
  }

  // Tạo vai trò mới
  const newRole = await VaiTro.create({
    ma_vai_tro: ma_vai_tro.toUpperCase(),
    ten_vai_tro,
    mo_ta
  });

  // Gán quyền cho vai trò
  if (quyen_ids.length > 0) {
    const permissions = await Quyen.findAll({
      where: { id: quyen_ids }
    });
    await newRole.setQuyenList(permissions);
  }

  // Lấy thông tin vai trò với quyền
  const roleWithPermissions = await VaiTro.findByPk(newRole.id, {
    include: [
      {
        model: Quyen,
        as: 'quyenList',
        through: { attributes: [] }
      }
    ]
  });

  res.status(201).json({
    success: true,
    message: 'Tạo vai trò thành công',
    data: { role: roleWithPermissions }
  });
}));

/**
 * @route PUT /api/roles/:id
 * @desc Cập nhật vai trò
 * @access Private (Admin only)
 */
router.put('/:id', requireAdmin, asyncHandler(async (req, res) => {
  const { ma_vai_tro, ten_vai_tro, mo_ta, quyen_ids } = req.body;

  const role = await VaiTro.findByPk(req.params.id);
  if (!role) {
    throw new AppError('Không tìm thấy vai trò', 404);
  }

  // Kiểm tra mã vai trò đã tồn tại (nếu thay đổi)
  if (ma_vai_tro && ma_vai_tro !== role.ma_vai_tro) {
    const existingRole = await VaiTro.findOne({ where: { ma_vai_tro } });
    if (existingRole) {
      throw new AppError('Mã vai trò đã tồn tại', 400);
    }
  }

  // Cập nhật thông tin vai trò
  await role.update({
    ma_vai_tro: ma_vai_tro ? ma_vai_tro.toUpperCase() : role.ma_vai_tro,
    ten_vai_tro: ten_vai_tro || role.ten_vai_tro,
    mo_ta: mo_ta !== undefined ? mo_ta : role.mo_ta
  });

  // Cập nhật quyền (nếu có)
  if (quyen_ids && Array.isArray(quyen_ids)) {
    const permissions = await Quyen.findAll({
      where: { id: quyen_ids }
    });
    await role.setQuyenList(permissions);
  }

  // Lấy thông tin vai trò đã cập nhật
  const updatedRole = await VaiTro.findByPk(role.id, {
    include: [
      {
        model: Quyen,
        as: 'quyenList',
        through: { attributes: [] }
      }
    ]
  });

  res.json({
    success: true,
    message: 'Cập nhật vai trò thành công',
    data: { role: updatedRole }
  });
}));

/**
 * @route DELETE /api/roles/:id
 * @desc Xóa vai trò
 * @access Private (Admin only)
 */
router.delete('/:id', requireAdmin, asyncHandler(async (req, res) => {
  const role = await VaiTro.findByPk(req.params.id);
  if (!role) {
    throw new AppError('Không tìm thấy vai trò', 404);
  }

  // Kiểm tra xem vai trò có đang được sử dụng không
  const usersWithRole = await NguoiDung.findAll({
    include: [
      {
        model: VaiTro,
        as: 'vaiTroList',
        where: { id: req.params.id }
      }
    ]
  });

  if (usersWithRole.length > 0) {
    throw new AppError('Không thể xóa vai trò đang được sử dụng', 400);
  }

  await role.destroy();

  res.json({
    success: true,
    message: 'Xóa vai trò thành công'
  });
}));

/**
 * @route POST /api/roles/:id/assign-permissions
 * @desc Gán quyền cho vai trò
 * @access Private (Admin only)
 */
router.post('/:id/assign-permissions', requireAdmin, asyncHandler(async (req, res) => {
  const { quyen_ids } = req.body;

  if (!Array.isArray(quyen_ids)) {
    throw new AppError('quyen_ids phải là một mảng', 400);
  }

  const role = await VaiTro.findByPk(req.params.id);
  if (!role) {
    throw new AppError('Không tìm thấy vai trò', 404);
  }

  const permissions = await Quyen.findAll({
    where: { id: quyen_ids }
  });

  await role.setQuyenList(permissions);

  res.json({
    success: true,
    message: 'Gán quyền thành công'
  });
}));

module.exports = router;
