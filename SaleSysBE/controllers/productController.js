const {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  GiaTri<PERSON><PERSON>,
  PhienBan<PERSON>,
  PhienBanGiaTri<PERSON>hu<PERSON>,
  TonKhoPhien<PERSON>an,
  AnhSan<PERSON>ham,
  KhoHang
} = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

/**
 * <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm
 */
const getProducts = async (req, res) => {
  const { page = 1, limit = 10, search, category_id, brand_id, status } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = {};

  // Search by name or code
  if (search) {
    whereClause[Op.or] = [
      { ten: { [Op.like]: `%${search}%` } },
      { ma: { [Op.like]: `%${search}%` } }
    ];
  }

  // Filter by category
  if (category_id) {
    whereClause.loai_san_pham_id = category_id;
  }

  // Filter by brand
  if (brand_id) {
    whereClause.nhan_hieu_id = brand_id;
  }

  // Filter by status
  if (status) {
    whereClause.trang_thai = status === 'active' ? 1 : 0;
  }

  // First, get products without variants to avoid duplicates
  const { count, rows } = await SanPham.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: LoaiSanPham,
        as: 'loaiSanPham',
        attributes: ['ten']
      },
      {
        model: NhanHieu,
        as: 'nhanHieu',
        attributes: ['ten']
      },
      {
        model: Tag,
        as: 'tag',
        attributes: ['ten']
      },
      {
        model: AnhSanPham,
        as: 'anhList',
        attributes: ['id', 'url', 'thumbnail_url', 'optimized_url'],
        order: [['id', 'ASC']] // Lấy ảnh theo thứ tự tạo
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_tao', 'DESC']]
  });

  // Then get variant counts separately
  const productIds = rows.map(p => p.id);
  const variantCounts = await PhienBanSanPham.findAll({
    where: { san_pham_id: { [Op.in]: productIds } },
    attributes: [
      'san_pham_id',
      [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'variant_count']
    ],
    group: ['san_pham_id']
  });

  // Create a map for quick lookup
  const variantCountMap = {};
  variantCounts.forEach(vc => {
    variantCountMap[vc.san_pham_id] = parseInt(vc.dataValues.variant_count);
  });

  const productsData = rows.map(product => {
    const data = product.toJSON();

    // Lấy ảnh đầu tiên để hiển thị
    const firstImage = data.anhList && data.anhList.length > 0 ? data.anhList[0] : null;

    return {
      ...data,
      trang_thai: data.trang_thai === 1 ? 'active' : 'inactive',
      loai_san_pham: data.loaiSanPham?.ten || null,
      nhan_hieu: data.nhanHieu?.ten || null,
      tag: data.tag?.ten || null,
      so_phien_ban: variantCountMap[data.id] || 0,
      anh_dai_dien: firstImage ? {
        id: firstImage.id,
        url: firstImage.url,
        thumbnail_url: firstImage.thumbnail_url,
        optimized_url: firstImage.optimized_url
      } : null,
      so_anh: data.anhList ? data.anhList.length : 0
    };
  });

  res.json({
    success: true,
    data: productsData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Lấy thông tin chi tiết sản phẩm
 */
const getProduct = async (req, res) => {
  const { id } = req.params;

  const product = await SanPham.findByPk(id, {
    include: [
      {
        model: LoaiSanPham,
        as: 'loaiSanPham'
      },
      {
        model: NhanHieu,
        as: 'nhanHieu'
      },
      {
        model: Tag,
        as: 'tag'
      },
      {
        model: PhienBanSanPham,
        as: 'phienBanList',
        include: [
          {
            model: GiaTriThuocTinh,
            as: 'giaTriThuocTinhList',
            through: { attributes: [] },
            include: [
              {
                model: ThuocTinh,
                as: 'thuocTinh'
              }
            ]
          }
        ]
      },
      {
        model: AnhSanPham,
        as: 'anhList'
      }
    ]
  });

  if (!product) {
    throw new AppError('Không tìm thấy sản phẩm', 404);
  }

  const productData = product.toJSON();
  
  res.json({
    success: true,
    data: {
      ...productData,
      trang_thai: productData.trang_thai === 1 ? 'active' : 'inactive'
    }
  });
};

/**
 * Tạo sản phẩm mới
 */
const createProduct = async (req, res) => {
  const { 
    ten, 
    ma, 
    mo_ta, 
    loai_san_pham_id, 
    nhan_hieu_id, 
    tag_id,
    phien_ban = [],
    anh_san_pham = [],
    inventory_data,
    unit_conversions
  } = req.body;

  // Validate required fields
  if (!ten) {
    throw new AppError('Tên sản phẩm là bắt buộc', 400);
  }

  // Check if product code already exists
  if (ma) {
    const existingProduct = await SanPham.findOne({ where: { ma } });
    if (existingProduct) {
      throw new AppError('Mã sản phẩm đã tồn tại', 400);
    }
  }

  // Create product
  const product = await SanPham.create({
    ten,
    ma,
    mo_ta,
    loai_san_pham_id,
    nhan_hieu_id,
    tag_id,
    trang_thai: 1,
    nguoi_tao: req.user?.username || 'system'
  });

  // Create product variants if provided
  if (phien_ban && phien_ban.length > 0) {
    for (const variant of phien_ban) {
      const createdVariant = await PhienBanSanPham.create({
        san_pham_id: product.id,
        ten_phien_ban: variant.ten_phien_ban,
        gia_le: variant.gia_le,
        gia_buon: variant.gia_buon,
        gia_nhap: variant.gia_nhap,
        don_vi_tinh: variant.don_vi_tinh,
        nguoi_tao: req.user?.username || 'system'
      });

      // Auto-initialize inventory for all warehouses
      const allWarehouses = await KhoHang.findAll({
        where: { trang_thai: 1 }, // Only active warehouses
        attributes: ['id']
      });

      for (const warehouse of allWarehouses) {
        // Check if inventory_data has specific data for this warehouse
        let inventoryInfo = null;
        if (inventory_data && inventory_data[warehouse.id]) {
          inventoryInfo = inventory_data[warehouse.id];
        }

        // Create inventory record for each warehouse
        await TonKhoPhienBan.create({
          phien_ban_san_pham_id: createdVariant.id,
          kho_hang_id: warehouse.id,
          so_luong_ton: inventoryInfo?.ton_kho || 0,
          gia_von: inventoryInfo?.gia_von || variant.gia_nhap || 0,
          so_luong_toi_thieu: inventoryInfo?.so_luong_toi_thieu || 0,
          nguoi_tao: req.user?.username || 'system'
        });
      }
    }
  }

  // Create product images if provided
  if (anh_san_pham && anh_san_pham.length > 0) {
    for (let i = 0; i < anh_san_pham.length; i++) {
      const image = anh_san_pham[i];
      await AnhSanPham.create({
        san_pham_id: product.id,
        url: image.url,
        public_id: image.public_id,
        thumbnail_url: image.thumbnail_url,
        optimized_url: image.optimized_url,
        nguoi_tao: req.user?.username || 'system'
      });
    }
  }

  // Get created product with relations
  const createdProduct = await SanPham.findByPk(product.id, {
    include: [
      {
        model: LoaiSanPham,
        as: 'loaiSanPham'
      },
      {
        model: NhanHieu,
        as: 'nhanHieu'
      },
      {
        model: Tag,
        as: 'tag'
      },
      {
        model: PhienBanSanPham,
        as: 'phienBanList'
      },
      {
        model: AnhSanPham,
        as: 'anhList',
        attributes: ['id', 'url', 'thumbnail_url', 'optimized_url'],
        order: [['id', 'ASC']]
      }
    ]
  });

  const productData = createdProduct.toJSON();

  // Lấy ảnh đầu tiên để hiển thị
  const firstImage = productData.anhList && productData.anhList.length > 0 ? productData.anhList[0] : null;

  res.status(201).json({
    success: true,
    message: 'Tạo sản phẩm thành công',
    data: {
      ...productData,
      trang_thai: 'active',
      anh_dai_dien: firstImage ? {
        id: firstImage.id,
        url: firstImage.url,
        thumbnail_url: firstImage.thumbnail_url,
        optimized_url: firstImage.optimized_url
      } : null,
      so_anh: productData.anhList ? productData.anhList.length : 0
    }
  });
};

/**
 * Cập nhật sản phẩm
 */
const updateProduct = async (req, res) => {
  const { id } = req.params;
  const { ten, ma, mo_ta, loai_san_pham_id, nhan_hieu_id, tag_id, trang_thai } = req.body;

  const product = await SanPham.findByPk(id);
  if (!product) {
    throw new AppError('Không tìm thấy sản phẩm', 404);
  }

  // Check if new code already exists (excluding current product)
  if (ma && ma !== product.ma) {
    const existingProduct = await SanPham.findOne({
      where: { 
        ma,
        id: { [Op.ne]: id }
      }
    });

    if (existingProduct) {
      throw new AppError('Mã sản phẩm đã tồn tại', 400);
    }
  }

  await product.update({
    ten: ten || product.ten,
    ma: ma || product.ma,
    mo_ta: mo_ta !== undefined ? mo_ta : product.mo_ta,
    loai_san_pham_id: loai_san_pham_id || product.loai_san_pham_id,
    nhan_hieu_id: nhan_hieu_id || product.nhan_hieu_id,
    tag_id: tag_id || product.tag_id,
    trang_thai: trang_thai !== undefined ? (trang_thai === 'active' ? 1 : 0) : product.trang_thai,
    nguoi_cap_nhap: req.user?.username || 'system'
  });

  res.json({
    success: true,
    message: 'Cập nhật sản phẩm thành công',
    data: {
      ...product.toJSON(),
      trang_thai: product.trang_thai === 1 ? 'active' : 'inactive'
    }
  });
};

/**
 * Xóa sản phẩm
 */
const deleteProduct = async (req, res) => {
  const { id } = req.params;

  const product = await SanPham.findByPk(id);
  if (!product) {
    throw new AppError('Không tìm thấy sản phẩm', 404);
  }

  // Check if product has variants with inventory
  const hasInventory = await TonKhoPhienBan.count({
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        where: { san_pham_id: id }
      }
    ]
  });

  if (hasInventory > 0) {
    throw new AppError('Không thể xóa sản phẩm có tồn kho', 400);
  }

  await product.destroy();

  res.json({
    success: true,
    message: 'Xóa sản phẩm thành công'
  });
};

/**
 * Lấy danh sách loại sản phẩm
 */
const getCategories = async (req, res) => {
  const categories = await LoaiSanPham.findAll({
    order: [['ten', 'ASC']]
  });

  res.json({
    success: true,
    data: categories
  });
};

/**
 * Tạo loại sản phẩm mới
 */
const createCategory = async (req, res) => {
  const { ten, mo_ta } = req.body;

  if (!ten) {
    throw new AppError('Tên loại sản phẩm là bắt buộc', 400);
  }

  // Check if category name already exists
  const existingCategory = await LoaiSanPham.findOne({ where: { ten } });
  if (existingCategory) {
    throw new AppError('Tên loại sản phẩm đã tồn tại', 400);
  }

  const category = await LoaiSanPham.create({
    ten,
    mo_ta,
    nguoi_tao: req.user?.username || 'system'
  });

  res.status(201).json({
    success: true,
    message: 'Tạo loại sản phẩm thành công',
    data: category
  });
};

/**
 * Cập nhật loại sản phẩm
 */
const updateCategory = async (req, res) => {
  const { id } = req.params;
  const { ten, mo_ta } = req.body;

  const category = await LoaiSanPham.findByPk(id);
  if (!category) {
    throw new AppError('Không tìm thấy loại sản phẩm', 404);
  }

  // Check if new name already exists (excluding current category)
  if (ten && ten !== category.ten) {
    const existingCategory = await LoaiSanPham.findOne({
      where: {
        ten,
        id: { [Op.ne]: id }
      }
    });

    if (existingCategory) {
      throw new AppError('Tên loại sản phẩm đã tồn tại', 400);
    }
  }

  await category.update({
    ten: ten || category.ten,
    mo_ta: mo_ta !== undefined ? mo_ta : category.mo_ta,
    nguoi_cap_nhap: req.user?.username || 'system'
  });

  res.json({
    success: true,
    message: 'Cập nhật loại sản phẩm thành công',
    data: category
  });
};

/**
 * Xóa loại sản phẩm
 */
const deleteCategory = async (req, res) => {
  const { id } = req.params;

  const category = await LoaiSanPham.findByPk(id);
  if (!category) {
    throw new AppError('Không tìm thấy loại sản phẩm', 404);
  }

  // Check if category is being used by products
  const productCount = await SanPham.count({
    where: { loai_san_pham_id: id }
  });

  if (productCount > 0) {
    throw new AppError(`Không thể xóa loại sản phẩm đang được sử dụng bởi ${productCount} sản phẩm`, 400);
  }

  await category.destroy();

  res.json({
    success: true,
    message: 'Xóa loại sản phẩm thành công'
  });
};

/**
 * Lấy danh sách nhãn hiệu
 */
const getBrands = async (req, res) => {
  const brands = await NhanHieu.findAll({
    order: [['ten', 'ASC']]
  });

  res.json({
    success: true,
    data: brands
  });
};

/**
 * Tạo nhãn hiệu mới
 */
const createBrand = async (req, res) => {
  const { ten, mo_ta, website, logo } = req.body;

  if (!ten) {
    throw new AppError('Tên nhãn hiệu là bắt buộc', 400);
  }

  // Check if brand name already exists
  const existingBrand = await NhanHieu.findOne({ where: { ten } });
  if (existingBrand) {
    throw new AppError('Tên nhãn hiệu đã tồn tại', 400);
  }

  const brand = await NhanHieu.create({
    ten,
    mo_ta,
    website,
    logo,
    nguoi_tao: req.user?.username || 'system'
  });

  res.status(201).json({
    success: true,
    message: 'Tạo nhãn hiệu thành công',
    data: brand
  });
};

/**
 * Cập nhật nhãn hiệu
 */
const updateBrand = async (req, res) => {
  const { id } = req.params;
  const { ten, mo_ta, website, logo } = req.body;

  const brand = await NhanHieu.findByPk(id);
  if (!brand) {
    throw new AppError('Không tìm thấy nhãn hiệu', 404);
  }

  // Check if new name already exists (excluding current brand)
  if (ten && ten !== brand.ten) {
    const existingBrand = await NhanHieu.findOne({
      where: {
        ten,
        id: { [Op.ne]: id }
      }
    });

    if (existingBrand) {
      throw new AppError('Tên nhãn hiệu đã tồn tại', 400);
    }
  }

  await brand.update({
    ten: ten || brand.ten,
    mo_ta: mo_ta !== undefined ? mo_ta : brand.mo_ta,
    website: website !== undefined ? website : brand.website,
    logo: logo !== undefined ? logo : brand.logo,
    nguoi_cap_nhap: req.user?.username || 'system'
  });

  res.json({
    success: true,
    message: 'Cập nhật nhãn hiệu thành công',
    data: brand
  });
};

/**
 * Xóa nhãn hiệu
 */
const deleteBrand = async (req, res) => {
  const { id } = req.params;

  const brand = await NhanHieu.findByPk(id);
  if (!brand) {
    throw new AppError('Không tìm thấy nhãn hiệu', 404);
  }

  // Check if brand is being used by products
  const productCount = await SanPham.count({
    where: { nhan_hieu_id: id }
  });

  if (productCount > 0) {
    throw new AppError(`Không thể xóa nhãn hiệu đang được sử dụng bởi ${productCount} sản phẩm`, 400);
  }

  await brand.destroy();

  res.json({
    success: true,
    message: 'Xóa nhãn hiệu thành công'
  });
};

/**
 * Lấy danh sách tag
 */
const getTags = async (req, res) => {
  const tags = await Tag.findAll({
    order: [['ten', 'ASC']]
  });

  res.json({
    success: true,
    data: tags
  });
};

/**
 * Lấy danh sách thuộc tính
 */
const getAttributes = async (req, res) => {
  const attributes = await ThuocTinh.findAll({
    include: [
      {
        model: GiaTriThuocTinh,
        as: 'giaTriList',
        attributes: ['id', 'gia_tri']
      }
    ],
    order: [['ten', 'ASC']]
  });

  const attributesData = attributes.map(attr => {
    const data = attr.toJSON();
    return {
      ...data,
      gia_tri: data.giaTriList?.map(gt => gt.gia_tri) || []
    };
  });

  res.json({
    success: true,
    data: attributesData
  });
};

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getBrands,
  createBrand,
  updateBrand,
  deleteBrand,
  getTags,
  getAttributes
};
