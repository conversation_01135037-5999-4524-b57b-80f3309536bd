const db = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

// Sử dụng model NguoiDung thay v<PERSON>ang
const NguoiDung = db.<PERSON>uo<PERSON>ung;
const NhomKhachHang = db.NhomK<PERSON>ch<PERSON>ang;
const DonHang = db.Don<PERSON>ang;
const sequelize = db.sequelize;
const HoSoCaNhan = db.<PERSON>o<PERSON>;

/**
 * Lấy danh sách khách hàng
 */
const getCustomers = async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search = '', 
    trang_thai = '',
    sort_by = 'ho_ten',
    sort_order = 'asc',
    nhom_khach_hang_id = ''
  } = req.query;

  // Xây dựng điều kiện tìm kiếm
  const whereClause = {
    loai_nguoi_dung: 'khach_hang' // Chỉ lấy người dùng có loại là khách hàng
  };
  
  // Tìm kiếm theo từ khóa
  if (search) {
    whereClause[Op.or] = [
      { id: { [Op.like]: `%${search}%` } }, // Thay cho ma_khach_hang
      { ho_ten: { [Op.like]: `%${search}%` } },
      { so_dien_thoai: { [Op.like]: `%${search}%` } }
    ];
  }

  // Lọc theo trạng thái
  if (trang_thai) {
    whereClause.trang_thai = trang_thai;
  }

  // Xác định thứ tự sắp xếp
  const order = [[sort_by, sort_order.toUpperCase()]];

  // Tính toán phân trang
  const offset = (page - 1) * limit;
  
  try {
    // Truy vấn danh sách khách hàng
    let customers = await NguoiDung.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] }, // Không lấy thông tin bảng trung gian
          attributes: ['id', 'ten_nhom']
        }
      ],
      order,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Lấy thêm thông tin tổng chi tiêu và số lượng đơn hàng
    const customerIds = customers.rows.map(customer => customer.id);
    
    // Lấy tổng chi tiêu và số lượng đơn hàng
    const customerStats = await DonHang.findAll({
      attributes: [
        'khach_hang_id',
        [sequelize.fn('SUM', sequelize.col('tong_tien')), 'tong_chi_tieu'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'tong_sl_don_hang']
      ],
      where: {
        khach_hang_id: { [Op.in]: customerIds },
        trang_thai: { [Op.ne]: 'huy' } // Không tính đơn hàng đã hủy
      },
      group: ['khach_hang_id']
    });

    // Tạo map để dễ dàng truy cập thông tin thống kê
    const statsMap = {};
    customerStats.forEach(stat => {
      statsMap[stat.khach_hang_id] = {
        tong_chi_tieu: parseInt(stat.getDataValue('tong_chi_tieu') || 0),
        tong_sl_don_hang: parseInt(stat.getDataValue('tong_sl_don_hang') || 0)
      };
    });

    // Nếu lọc theo trạng thái "đang giao dịch", chỉ lấy khách hàng có đơn hàng
    if (trang_thai === 'dang_giao_dich') {
      const activeCustomerIds = customerStats.map(stat => stat.khach_hang_id);
      customers.rows = customers.rows.filter(customer => 
        activeCustomerIds.includes(customer.id)
      );
      customers.count = customers.rows.length;
    }

    // Format dữ liệu trả về
    const formattedCustomers = customers.rows.map(customer => {
      const stats = statsMap[customer.id] || { tong_chi_tieu: 0, tong_sl_don_hang: 0 };
      // Lấy nhóm khách hàng đầu tiên nếu có
      const nhomKhachHang = customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
        ? customer.nhomKhachHangList[0].ten_nhom 
        : 'Bán lẻ';
      
      return {
        id: customer.id,
        ma_khach_hang: `KH${String(customer.id).padStart(5, '0')}`, // Tạo mã khách hàng từ ID
        ho_ten: customer.ho_ten,
        so_dien_thoai: customer.so_dien_thoai || '',
        nhom_khach_hang: nhomKhachHang,
        cong_no_hien_tai: customer.congNo?.tong_cong_no || 0,
        tong_chi_tieu: stats.tong_chi_tieu || 0,
        tong_sl_don_hang: stats.tong_sl_don_hang || 0
      };
    });

    res.json({
      success: true,
      data: {
        customers: formattedCustomers,
        pagination: {
          total: customers.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(customers.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error in getCustomers:', error);
    throw new AppError(`Failed to fetch customers: ${error.message}`, 500);
  }
};

/**
 * Lấy thông tin chi tiết khách hàng
 */
const getCustomer = async (req, res) => {
  const { id } = req.params;

  try {
    const customer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] }
        },
        {
          model: db.CongNoNguoiDung,
          as: 'congNo'
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false,
          limit: 1
        }
      ]
    });

    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Lấy thông tin đơn hàng
    const orderStats = await DonHang.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('tong_tien')), 'tong_chi_tieu'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'tong_sl_don_hang']
      ],
      where: {
        khach_hang_id: id,
        trang_thai: { [Op.ne]: 'huy' }
      }
    });

    const stats = orderStats[0] || {};
    
    // Lấy nhóm khách hàng đầu tiên nếu có
    const nhomKhachHang = customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
      ? customer.nhomKhachHangList[0]
      : null;
    
    // Lấy địa chỉ mặc định
    const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
      ? customer.diaChiList[0] 
      : null;
    
    const customerData = {
      id: customer.id,
      ma_khach_hang: `KH${String(customer.id).padStart(5, '0')}`,
      ho_ten: customer.ho_ten,
      email: customer.email,
      so_dien_thoai: customer.so_dien_thoai,
      dia_chi: defaultAddress?.dia_chi || '',
      phuong_xa: defaultAddress?.phuong_xa || '',
      quan_huyen: defaultAddress?.quan_huyen || '',
      tinh_thanh: defaultAddress?.tinh_thanh || '',
      nhomKhachHang: nhomKhachHang,
      trang_thai: customer.trang_thai,
      ghi_chu: customer.hoSoCaNhan?.mo_ta || '',
      cong_no_hien_tai: customer.congNo?.tong_cong_no || 0,
      tong_chi_tieu: parseInt(stats.getDataValue('tong_chi_tieu') || 0),
      tong_sl_don_hang: parseInt(stats.getDataValue('tong_sl_don_hang') || 0),
      nguoi_tao: customer.nguoi_tao,
      ngay_tao: customer.createdAt
    };

    res.json({
      success: true,
      data: { customer: customerData }
    });
  } catch (error) {
    console.error('Error in getCustomer:', error);
    throw new AppError(`Failed to fetch customer: ${error.message}`, 500);
  }
};

/**
 * Tạo khách hàng mới
 */
const createCustomer = async (req, res) => {
  const { 
    ho_ten, 
    email, 
    so_dien_thoai, 
    dia_chi, 
    phuong_xa,
    quan_huyen,
    tinh_thanh,
    nhom_khach_hang_id,
    ghi_chu
  } = req.body;

  // Validate dữ liệu
  if (!ho_ten) {
    throw new AppError('Tên khách hàng là bắt buộc', 400);
  }

  // Kiểm tra email đã tồn tại
  if (email) {
    const existingCustomer = await NguoiDung.findOne({ where: { email } });
    if (existingCustomer) {
      throw new AppError('Email đã được sử dụng', 400);
    }
  }

  // Kiểm tra số điện thoại đã tồn tại
  if (so_dien_thoai) {
    const existingCustomer = await NguoiDung.findOne({ where: { so_dien_thoai } });
    if (existingCustomer) {
      throw new AppError('Số điện thoại đã được sử dụng', 400);
    }
  }

  try {
    // Bắt đầu transaction
    const result = await sequelize.transaction(async (t) => {
      // Tạo người dùng mới
      const newCustomer = await NguoiDung.create({
        ho_ten,
        email,
        so_dien_thoai,
        loai_nguoi_dung: 'khach_hang',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: req.user?.email || 'system'
      }, { transaction: t });

      // Tạo hồ sơ cá nhân nếu có ghi chú
      if (ghi_chu) {
        try {
          await db.HoSoCaNhan.create({
            nguoi_dung_id: newCustomer.id,
            mo_ta: ghi_chu
          }, { transaction: t });
        } catch (error) {
          console.error('Error creating HoSoCaNhan:', error);
          // Tiếp tục mà không tạo hồ sơ cá nhân
        }
      }

      // Tạo địa chỉ nếu có
      if (dia_chi) {
        try {
          await db.DiaChiNguoiDung.create({
            nguoi_dung_id: newCustomer.id,
            dia_chi,
            phuong_xa,
            quan_huyen,
            tinh_thanh,
            mac_dinh: true
          }, { transaction: t });
        } catch (error) {
          console.error('Error creating DiaChiNguoiDung:', error);
          // Tiếp tục mà không tạo địa chỉ
        }
      }

      // Tạo công nợ
      try {
        await db.CongNoNguoiDung.create({
          nguoi_dung_id: newCustomer.id,
          tong_cong_no: 0
        }, { transaction: t });
      } catch (error) {
        console.error('Error creating CongNoNguoiDung:', error);
        // Tiếp tục mà không tạo công nợ
      }

      // Thêm vào nhóm khách hàng nếu có
      if (nhom_khach_hang_id) {
        try {
          await db.NhomKhachHangNguoiDung.create({
            nguoi_dung_id: newCustomer.id,
            nhom_khach_hang_id
          }, { transaction: t });
        } catch (error) {
          console.error('Error creating NhomKhachHangNguoiDung:', error);
          // Tiếp tục mà không thêm vào nhóm
        }
      }

      return newCustomer;
    });

    // Lấy thông tin khách hàng đã tạo
    const customer = await NguoiDung.findByPk(result.id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] },
          required: false
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan',
          required: false
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          required: false,
          where: { mac_dinh: true },
          limit: 1
        }
      ]
    });

    // Lấy địa chỉ mặc định
    const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
      ? customer.diaChiList[0] 
      : null;

    // Chuẩn bị dữ liệu phản hồi
    const customerData = {
      id: customer.id,
      ma_khach_hang: `KH${String(customer.id).padStart(5, '0')}`,
      ho_ten: customer.ho_ten,
      email: customer.email,
      so_dien_thoai: customer.so_dien_thoai,
      dia_chi: defaultAddress?.dia_chi || '',
      phuong_xa: defaultAddress?.phuong_xa || '',
      quan_huyen: defaultAddress?.quan_huyen || '',
      tinh_thanh: defaultAddress?.tinh_thanh || '',
      nhomKhachHang: customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
        ? customer.nhomKhachHangList[0] 
        : null,
      trang_thai: customer.trang_thai,
      ghi_chu: customer.hoSoCaNhan?.mo_ta || '',
      cong_no_hien_tai: 0,
      tong_chi_tieu: 0,
      tong_sl_don_hang: 0
    };

    res.status(201).json({
      success: true,
      message: 'Tạo khách hàng thành công',
      data: { customer: customerData }
    });
  } catch (error) {
    console.error('Error in createCustomer:', error);
    throw new AppError(`Failed to create customer: ${error.message}`, 500);
  }
};

/**
 * Cập nhật khách hàng
 */
const updateCustomer = async (req, res) => {
  const { id } = req.params;
  const { 
    ho_ten, 
    email, 
    so_dien_thoai, 
    dia_chi,
    phuong_xa,
    quan_huyen,
    tinh_thanh,
    nhom_khach_hang_id,
    ghi_chu,
    trang_thai
  } = req.body;

  try {
    const customer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false
        }
      ]
    });
    
    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Kiểm tra email đã tồn tại
    if (email && email !== customer.email) {
      const existingCustomer = await NguoiDung.findOne({ where: { email } });
      if (existingCustomer) {
        throw new AppError('Email đã được sử dụng', 400);
      }
    }

    // Kiểm tra số điện thoại đã tồn tại
    if (so_dien_thoai && so_dien_thoai !== customer.so_dien_thoai) {
      const existingCustomer = await NguoiDung.findOne({ where: { so_dien_thoai } });
      if (existingCustomer) {
        throw new AppError('Số điện thoại đã được sử dụng', 400);
      }
    }

    // Bắt đầu transaction
    await sequelize.transaction(async (t) => {
      // Cập nhật thông tin khách hàng
      await customer.update({
        ho_ten: ho_ten || customer.ho_ten,
        email: email || customer.email,
        so_dien_thoai: so_dien_thoai || customer.so_dien_thoai,
        trang_thai: trang_thai || customer.trang_thai,
        nguoi_cap_nhap: req.user?.email || 'system'
      }, { transaction: t });

      // Cập nhật hồ sơ cá nhân
      if (ghi_chu !== undefined) {
        if (customer.hoSoCaNhan) {
          await customer.hoSoCaNhan.update({
            mo_ta: ghi_chu
          }, { transaction: t });
        } else {
          // Tạo mới nếu chưa có
          await db.HoSoCaNhan.create({
            nguoi_dung_id: customer.id,
            mo_ta: ghi_chu
          }, { transaction: t });
        }
      }

      // Cập nhật địa chỉ
      if (dia_chi !== undefined) {
        const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
          ? customer.diaChiList[0] 
          : null;
          
        if (defaultAddress) {
          // Cập nhật địa chỉ hiện có
          await defaultAddress.update({
            dia_chi,
            phuong_xa: phuong_xa || defaultAddress.phuong_xa,
            quan_huyen: quan_huyen || defaultAddress.quan_huyen,
            tinh_thanh: tinh_thanh || defaultAddress.tinh_thanh
          }, { transaction: t });
        } else if (dia_chi) {
          // Tạo địa chỉ mới nếu chưa có
          await db.DiaChiNguoiDung.create({
            nguoi_dung_id: customer.id,
            dia_chi,
            phuong_xa,
            quan_huyen,
            tinh_thanh,
            mac_dinh: true
          }, { transaction: t });
        }
      }

      // Cập nhật nhóm khách hàng
      if (nhom_khach_hang_id) {
        // Xóa tất cả nhóm hiện tại
        await db.NhomKhachHangNguoiDung.destroy({
          where: { nguoi_dung_id: customer.id },
          transaction: t
        });
        
        // Thêm nhóm mới
        await db.NhomKhachHangNguoiDung.create({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id
        }, { transaction: t });
      }
    });

    // Lấy thông tin khách hàng đã cập nhật
    const updatedCustomer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] }
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.CongNoNguoiDung,
          as: 'congNo'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false,
          limit: 1
        }
      ]
    });

    // Lấy thông tin đơn hàng
    const orderStats = await DonHang.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('tong_tien')), 'tong_chi_tieu'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'tong_sl_don_hang']
      ],
      where: {
        khach_hang_id: id,
        trang_thai: { [Op.ne]: 'huy' }
      }
    });

    const stats = orderStats[0] || {};
    
    // Lấy nhóm khách hàng đầu tiên nếu có
    const nhomKhachHang = updatedCustomer.nhomKhachHangList && updatedCustomer.nhomKhachHangList.length > 0 
      ? updatedCustomer.nhomKhachHangList[0]
      : null;
      
    // Lấy địa chỉ mặc định
    const defaultAddress = updatedCustomer.diaChiList && updatedCustomer.diaChiList.length > 0 
      ? updatedCustomer.diaChiList[0] 
      : null;

    res.json({
      success: true,
      message: 'Cập nhật khách hàng thành công',
      data: { 
        customer: {
          id: updatedCustomer.id,
          ma_khach_hang: `KH${String(updatedCustomer.id).padStart(5, '0')}`,
          ho_ten: updatedCustomer.ho_ten,
          email: updatedCustomer.email,
          so_dien_thoai: updatedCustomer.so_dien_thoai,
          dia_chi: defaultAddress?.dia_chi || '',
          phuong_xa: defaultAddress?.phuong_xa || '',
          quan_huyen: defaultAddress?.quan_huyen || '',
          tinh_thanh: defaultAddress?.tinh_thanh || '',
          nhomKhachHang: nhomKhachHang,
          trang_thai: updatedCustomer.trang_thai,
          ghi_chu: updatedCustomer.hoSoCaNhan?.mo_ta || '',
          cong_no_hien_tai: updatedCustomer.congNo?.tong_cong_no || 0,
          tong_chi_tieu: parseInt(stats.getDataValue('tong_chi_tieu') || 0),
          tong_sl_don_hang: parseInt(stats.getDataValue('tong_sl_don_hang') || 0),
          nguoi_tao: updatedCustomer.nguoi_tao,
          ngay_tao: updatedCustomer.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Error in updateCustomer:', error);
    throw new AppError(`Failed to update customer: ${error.message}`, 500);
  }
};

/**
 * Xóa khách hàng
 */
const deleteCustomer = async (req, res) => {
  const { id } = req.params;

  try {
    const customer = await NguoiDung.findByPk(id);
    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Kiểm tra xem khách hàng có đơn hàng không
    const orderCount = await DonHang.count({
      where: { khach_hang_id: id }
    });

    if (orderCount > 0) {
      throw new AppError('Không thể xóa khách hàng đã có đơn hàng', 400);
    }

    // Bắt đầu transaction
    await sequelize.transaction(async (t) => {
      // Xóa các bản ghi liên quan
      await db.NhomKhachHangNguoiDung.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      await db.HoSoCaNhan.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      await db.CongNoNguoiDung.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      // Xóa người dùng
      await customer.destroy({ transaction: t });
    });

    res.json({
      success: true,
      message: 'Xóa khách hàng thành công'
    });
  } catch (error) {
    console.error('Error in deleteCustomer:', error);
    throw new AppError(`Failed to delete customer: ${error.message}`, 500);
  }
};

/**
 * Xuất danh sách khách hàng
 */
const exportCustomers = async (req, res) => {
  // TODO: Implement export functionality
  res.json({
    success: true,
    message: 'API xuất khách hàng đang được phát triển'
  });
};

/**
 * Nhập danh sách khách hàng từ file
 */
const importCustomers = async (req, res) => {
  // TODO: Implement import functionality
  res.json({
    success: true,
    message: 'API nhập khách hàng đang được phát triển'
  });
};

module.exports = {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  exportCustomers,
  importCustomers
};
