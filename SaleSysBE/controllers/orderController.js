const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>en<PERSON><PERSON>, SanPham, NguoiDung, TonKhoPhienBan } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

// Lấy danh sách đơn hàng
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      trang_thai,
      tu_ngay,
      den_ngay,
      khach_hang_id
    } = req.query;

    const offset = (page - 1) * limit;
    let whereCondition = {};

    // Tìm kiếm theo mã đơn hàng, tên khách hàng, SĐT
    if (search) {
      whereCondition[Op.or] = [
        { ma_don_hang: { [Op.like]: `%${search}%` } },
        { ten_khach_hang: { [Op.like]: `%${search}%` } },
        { so_dien_thoai: { [Op.like]: `%${search}%` } }
      ];
    }

    // Lọc theo trạng thái
    if (trang_thai) {
      whereCondition.trang_thai = trang_thai;
    }

    // Lọc theo khách hàng
    if (khach_hang_id) {
      whereCondition.khach_hang_id = khach_hang_id;
    }

    // Lọc theo ngày
    if (tu_ngay && den_ngay) {
      whereCondition.ngay_dat_hang = {
        [Op.between]: [tu_ngay, den_ngay]
      };
    } else if (tu_ngay) {
      whereCondition.ngay_dat_hang = {
        [Op.gte]: tu_ngay
      };
    } else if (den_ngay) {
      whereCondition.ngay_dat_hang = {
        [Op.lte]: den_ngay
      };
    }

    const { count, rows: orders } = await DonHang.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: DonHangSanPham,
          as: 'sanPhamList',
          include: [{
            model: PhienBanSanPham,
            as: 'phienBanSanPham',
            include: [{
              model: SanPham,
              attributes: ['ten_san_pham', 'anh']
            }]
          }]
        },
        {
          model: NguoiDung,
          as: 'nhanVienBan',
          attributes: ['ho_ten', 'email']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['ngay_tao', 'DESC']]
    });

    // Tính toán thống kê
    const stats = await calculateOrderStats();

    // Format dữ liệu trả về
    const formattedOrders = orders.map(order => ({
      id: order.id,
      ma_don_hang: order.ma_don_hang,
      ten_khach_hang: order.ten_khach_hang,
      so_dien_thoai: order.so_dien_thoai,
      dia_chi: order.dia_chi,
      ngay_dat_hang: order.ngay_dat_hang,
      trang_thai: order.trang_thai,
      tong_tien_hang: order.tong_tien_hang,
      giam_gia: order.giam_gia,
      tong_thanh_toan: order.tong_thanh_toan,
      ghi_chu: order.ghi_chu,
      so_luong_san_pham: order.sanPhamList?.length || 0,
      nguoi_tao: order.nhanVienBan?.ho_ten,
      ngay_tao: order.ngay_tao
    }));

    res.json({
      success: true,
      data: formattedOrders,
      total: count,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Error getting orders:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi lấy danh sách đơn hàng'
    });
  }
};

// Lấy chi tiết đơn hàng
const getOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await DonHang.findByPk(id, {
      include: [
        {
          model: DonHangSanPham,
          as: 'sanPhamList',
          include: [{
            model: PhienBanSanPham,
            as: 'phienBanSanPham',
            include: [{
              model: SanPham,
              attributes: ['ten_san_pham', 'anh']
            }]
          }]
        },
        {
          model: NguoiDung,
          as: 'nhanVienBan',
          attributes: ['ho_ten', 'email']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đơn hàng'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error getting order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi lấy thông tin đơn hàng'
    });
  }
};

// Tạo đơn hàng mới
const createOrder = async (req, res) => {
  const transaction = await DonHang.sequelize.transaction();
  
  try {
    const {
      ten_khach_hang,
      so_dien_thoai,
      dia_chi,
      ngay_dat_hang,
      trang_thai = 'cho_xac_nhan',
      ghi_chu,
      chi_tiet_don_hang,
      giam_gia = 0,
      loai_giam_gia = 'amount'
    } = req.body;

    // Validate chi tiết đơn hàng
    if (!chi_tiet_don_hang || chi_tiet_don_hang.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Đơn hàng phải có ít nhất một sản phẩm'
      });
    }

    // Tạo mã đơn hàng
    const ma_don_hang = await generateOrderCode();

    // Tính tổng tiền
    let tong_tien_hang = 0;
    for (const item of chi_tiet_don_hang) {
      tong_tien_hang += item.so_luong * item.gia_ban;
    }

    // Tính giảm giá
    const giam_gia_amount = loai_giam_gia === 'percent' 
      ? (tong_tien_hang * giam_gia / 100)
      : giam_gia;

    const tong_thanh_toan = tong_tien_hang - giam_gia_amount;

    // Tạo đơn hàng
    const order = await DonHang.create({
      ma_don_hang,
      ten_khach_hang,
      so_dien_thoai,
      dia_chi,
      ngay_dat_hang: ngay_dat_hang || new Date(),
      trang_thai,
      tong_tien_hang,
      giam_gia: giam_gia_amount,
      tong_thanh_toan,
      ghi_chu,
      nhan_vien_ban_id: req.user.id,
      ngay_tao: new Date(),
      ngay_cap_nhap: new Date()
    }, { transaction });

    // Tạo chi tiết đơn hàng
    for (const item of chi_tiet_don_hang) {
      await DonHangSanPham.create({
        don_hang_id: order.id,
        phien_ban_san_pham_id: item.phien_ban_san_pham_id,
        so_luong: item.so_luong,
        gia_ban: item.gia_ban,
        thanh_tien: item.so_luong * item.gia_ban,
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }, { transaction });

      // Cập nhật tồn kho (trừ tồn kho)
      if (trang_thai === 'da_xac_nhan' || trang_thai === 'hoan_thanh') {
        await updateInventoryForOrder(item.phien_ban_san_pham_id, -item.so_luong, transaction);
      }
    }

    await transaction.commit();

    // Lấy đơn hàng vừa tạo với đầy đủ thông tin
    const newOrder = await getOrder({ params: { id: order.id } }, res);

  } catch (error) {
    await transaction.rollback();
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi tạo đơn hàng'
    });
  }
};

// Cập nhật đơn hàng
const updateOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      ngay_cap_nhap: new Date()
    };

    const [updatedRowsCount] = await DonHang.update(updateData, {
      where: { id }
    });

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đơn hàng'
      });
    }

    // Lấy đơn hàng đã cập nhật
    const updatedOrder = await DonHang.findByPk(id);

    res.json({
      success: true,
      message: 'Cập nhật đơn hàng thành công',
      data: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi cập nhật đơn hàng'
    });
  }
};

// Helper functions
const generateOrderCode = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  
  const lastOrder = await DonHang.findOne({
    where: {
      ma_don_hang: {
        [Op.like]: `DH${dateStr}%`
      }
    },
    order: [['ma_don_hang', 'DESC']]
  });

  let sequence = 1;
  if (lastOrder) {
    const lastSequence = parseInt(lastOrder.ma_don_hang.slice(-4));
    sequence = lastSequence + 1;
  }

  return `DH${dateStr}${sequence.toString().padStart(4, '0')}`;
};

const calculateOrderStats = async () => {
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  const [totalOrders, pendingOrders, completedOrders, totalRevenue] = await Promise.all([
    DonHang.count(),
    DonHang.count({ where: { trang_thai: 'cho_xac_nhan' } }),
    DonHang.count({ where: { trang_thai: 'hoan_thanh' } }),
    DonHang.sum('tong_thanh_toan', {
      where: {
        trang_thai: 'hoan_thanh',
        ngay_dat_hang: { [Op.gte]: startOfMonth }
      }
    })
  ]);

  return {
    total_orders: totalOrders || 0,
    pending_orders: pendingOrders || 0,
    completed_orders: completedOrders || 0,
    total_revenue: totalRevenue || 0
  };
};

const updateInventoryForOrder = async (variantId, quantity, transaction) => {
  // Cập nhật tồn kho cho phiên bản sản phẩm
  const inventory = await TonKhoPhienBan.findOne({
    where: { phien_ban_san_pham_id: variantId }
  });

  if (inventory) {
    await inventory.update({
      so_luong: inventory.so_luong + quantity,
      ngay_cap_nhap: new Date()
    }, { transaction });
  }
};

module.exports = {
  getOrders,
  getOrder,
  createOrder,
  updateOrder
};
