const { <PERSON><PERSON><PERSON>iem<PERSON>, ChiTietKiemKe, KhoHang, PhienBanSan<PERSON>ham, SanPham, TonKhoPhien<PERSON>an, LichSuKho, sequelize } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

/**
 * L<PERSON>y danh sách phiếu kiểm kê
 */
const getStockChecks = async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    kho_id = '',
    trang_thai = ''
  } = req.query;

  const offset = (page - 1) * limit;
  
  // Build where clause
  const whereClause = {};
  
  if (search) {
    whereClause[Op.or] = [
      { ma_kiem_ke: { [Op.like]: `%${search}%` } },
      { ten_kiem_ke: { [Op.like]: `%${search}%` } },
      { nguoi_kiem_ke: { [Op.like]: `%${search}%` } }
    ];
  }
  
  if (kho_id) {
    whereClause.kho_hang_id = kho_id;
  }
  
  if (trang_thai) {
    whereClause.trang_thai = trang_thai;
  }

  const { count, rows } = await PhieuKiemKe.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: KhoHang,
        as: 'khoHang',
        attributes: ['id', 'ten_kho', 'dia_chi']
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_tao', 'DESC']]
  });

  // Format data for frontend
  const stockChecksData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.id,
      ma_kiem_ke: data.ma_kiem_ke,
      ten_kiem_ke: data.ten_kiem_ke,
      kho_id: data.kho_hang_id,
      ten_kho: data.khoHang?.ten_kho || 'N/A',
      trang_thai: data.trang_thai,
      ngay_bat_dau: data.ngay_bat_dau,
      ngay_ket_thuc: data.ngay_ket_thuc,
      nguoi_kiem_ke: data.nguoi_kiem_ke,
      tong_san_pham: data.tong_san_pham,
      san_pham_da_kiem: data.san_pham_da_kiem,
      san_pham_lech: data.san_pham_lech,
      gia_tri_lech: parseFloat(data.gia_tri_lech || 0),
      ghi_chu: data.ghi_chu,
      nguoi_tao: data.nguoi_tao,
      ngay_tao: data.ngay_tao,
      ngay_cap_nhap: data.ngay_cap_nhap
    };
  });

  res.json({
    success: true,
    data: stockChecksData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Lấy chi tiết phiếu kiểm kê
 */
const getStockCheck = async (req, res) => {
  const { id } = req.params;

  const stockCheck = await PhieuKiemKe.findByPk(id, {
    include: [
      {
        model: KhoHang,
        as: 'khoHang',
        attributes: ['id', 'ten_kho', 'dia_chi']
      },
      {
        model: ChiTietKiemKe,
        as: 'chiTietKiemKeList',
        include: [
          {
            model: PhienBanSanPham,
            as: 'phienBanSanPham',
            include: [
              {
                model: SanPham,
                as: 'sanPham',
                attributes: ['id', 'ten', 'ma']
              }
            ]
          }
        ]
      }
    ]
  });

  if (!stockCheck) {
    throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
  }

  res.json({
    success: true,
    data: stockCheck
  });
};

/**
 * Tạo phiếu kiểm kê mới
 */
const createStockCheck = async (req, res) => {
  const {
    ten_kiem_ke,
    kho_hang_id,
    ngay_bat_dau,
    ngay_ket_thuc,
    ghi_chu
  } = req.body;

  if (!ten_kiem_ke || !kho_hang_id || !ngay_bat_dau) {
    throw new AppError('Thiếu thông tin bắt buộc', 400);
  }

  // Kiểm tra kho hàng tồn tại
  const warehouse = await KhoHang.findByPk(kho_hang_id);
  if (!warehouse) {
    throw new AppError('Kho hàng không tồn tại', 404);
  }

  // Tạo mã kiểm kê tự động
  const lastCheck = await PhieuKiemKe.findOne({
    order: [['id', 'DESC']]
  });
  
  const nextNumber = lastCheck ? lastCheck.id + 1 : 1;
  const ma_kiem_ke = `KK${String(nextNumber).padStart(6, '0')}`;

  const transaction = await sequelize.transaction();

  try {
    // Tạo phiếu kiểm kê (không tạo chi tiết ngay)
    const stockCheck = await PhieuKiemKe.create({
      ma_kiem_ke,
      ten_kiem_ke,
      kho_hang_id,
      ngay_bat_dau,
      ngay_ket_thuc,
      nguoi_kiem_ke: req.user?.ho_ten || req.user?.username || 'system',
      tong_san_pham: 0, // Sẽ cập nhật khi thêm sản phẩm
      ghi_chu: ghi_chu || '',
      nguoi_tao: req.user?.username || 'system'
    }, { transaction });

    await transaction.commit();

    res.status(201).json({
      success: true,
      message: 'Tạo phiếu kiểm kê thành công. Hãy thêm sản phẩm cần kiểm kê.',
      data: {
        id: stockCheck.id,
        ma_kiem_ke: stockCheck.ma_kiem_ke,
        ten_kiem_ke: stockCheck.ten_kiem_ke,
        tong_san_pham: stockCheck.tong_san_pham
      }
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Cập nhật phiếu kiểm kê
 */
const updateStockCheck = async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const stockCheck = await PhieuKiemKe.findByPk(id);
  if (!stockCheck) {
    throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
  }

  // Không cho phép cập nhật phiếu đã hoàn thành
  if (stockCheck.trang_thai === 'hoan_thanh') {
    throw new AppError('Không thể cập nhật phiếu kiểm kê đã hoàn thành', 400);
  }

  await stockCheck.update({
    ...updateData,
    nguoi_cap_nhap: req.user?.username || 'system',
    ngay_cap_nhap: new Date()
  });

  res.json({
    success: true,
    message: 'Cập nhật phiếu kiểm kê thành công',
    data: stockCheck
  });
};

/**
 * Xóa phiếu kiểm kê
 */
const deleteStockCheck = async (req, res) => {
  const { id } = req.params;

  const stockCheck = await PhieuKiemKe.findByPk(id);
  if (!stockCheck) {
    throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
  }

  // Chỉ cho phép xóa phiếu ở trạng thái kế hoạch
  if (stockCheck.trang_thai !== 'ke_hoach') {
    throw new AppError('Chỉ có thể xóa phiếu kiểm kê ở trạng thái kế hoạch', 400);
  }

  await stockCheck.destroy();

  res.json({
    success: true,
    message: 'Xóa phiếu kiểm kê thành công'
  });
};

/**
 * Lấy danh sách sản phẩm có thể thêm vào kiểm kê
 */
const getAvailableProducts = async (req, res) => {
  const { id } = req.params;
  const { page = 1, limit = 20, search = '' } = req.query;
  const offset = (page - 1) * limit;

  const stockCheck = await PhieuKiemKe.findByPk(id);
  if (!stockCheck) {
    throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
  }

  // Lấy danh sách sản phẩm đã có trong phiếu kiểm kê
  const existingProducts = await ChiTietKiemKe.findAll({
    where: { phieu_kiem_ke_id: id },
    attributes: ['phien_ban_san_pham_id']
  });
  const existingProductIds = existingProducts.map(item => item.phien_ban_san_pham_id);

  // Build where clause for search
  const whereClause = {
    kho_hang_id: stockCheck.kho_hang_id,
    so_luong_ton: { [Op.gt]: 0 } // Chỉ lấy sản phẩm có tồn kho > 0
  };

  if (existingProductIds.length > 0) {
    whereClause.phien_ban_san_pham_id = { [Op.notIn]: existingProductIds };
  }

  const includeWhere = {};
  if (search) {
    includeWhere[Op.or] = [
      { ten_phien_ban: { [Op.like]: `%${search}%` } },
      { ma: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows } = await TonKhoPhienBan.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        where: Object.keys(includeWhere).length > 0 ? includeWhere : undefined,
        include: [
          {
            model: SanPham,
            as: 'sanPham',
            attributes: ['id', 'ten', 'ma']
          }
        ]
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['phien_ban_san_pham_id', 'ASC']]
  });

  // Format data for frontend
  const productsData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.phien_ban_san_pham_id,
      phien_ban_id: data.phien_ban_san_pham_id,
      ten_san_pham: data.phienBanSanPham?.sanPham?.ten || 'N/A',
      ten_phien_ban: data.phienBanSanPham?.ten_phien_ban || 'N/A',
      ma_sku: data.phienBanSanPham?.ma || 'N/A',
      anh: data.phienBanSanPham?.anh || null,
      don_vi_tinh: data.phienBanSanPham?.don_vi_tinh || 'Cái',
      so_luong_ton: data.so_luong_ton || 0,
      gia_von: parseFloat(data.gia_von || 0)
    };
  });

  res.json({
    success: true,
    data: productsData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Thêm sản phẩm vào phiếu kiểm kê
 */
const addProductToStockCheck = async (req, res) => {
  const { id } = req.params;
  const { phien_ban_san_pham_ids } = req.body; // Array of product variant IDs

  if (!phien_ban_san_pham_ids || !Array.isArray(phien_ban_san_pham_ids) || phien_ban_san_pham_ids.length === 0) {
    throw new AppError('Vui lòng chọn ít nhất một sản phẩm', 400);
  }

  const transaction = await sequelize.transaction();

  try {
    const stockCheck = await PhieuKiemKe.findByPk(id, { transaction });
    if (!stockCheck) {
      throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
    }

    if (stockCheck.trang_thai === 'hoan_thanh') {
      throw new AppError('Không thể thêm sản phẩm vào phiếu kiểm kê đã hoàn thành', 400);
    }

    // Lấy thông tin tồn kho của các sản phẩm
    const inventoryItems = await TonKhoPhienBan.findAll({
      where: {
        kho_hang_id: stockCheck.kho_hang_id,
        phien_ban_san_pham_id: { [Op.in]: phien_ban_san_pham_ids }
      },
      include: [
        {
          model: PhienBanSanPham,
          as: 'phienBanSanPham'
        }
      ],
      transaction
    });

    if (inventoryItems.length !== phien_ban_san_pham_ids.length) {
      throw new AppError('Một số sản phẩm không tồn tại trong kho này', 400);
    }

    // Kiểm tra sản phẩm đã có trong phiếu kiểm kê chưa
    const existingItems = await ChiTietKiemKe.findAll({
      where: {
        phieu_kiem_ke_id: id,
        phien_ban_san_pham_id: { [Op.in]: phien_ban_san_pham_ids }
      },
      transaction
    });

    if (existingItems.length > 0) {
      throw new AppError('Một số sản phẩm đã có trong phiếu kiểm kê', 400);
    }

    // Tạo chi tiết kiểm kê
    const chiTietData = inventoryItems.map(item => ({
      phieu_kiem_ke_id: id,
      phien_ban_san_pham_id: item.phien_ban_san_pham_id,
      so_luong_he_thong: item.so_luong_ton || 0,
      gia_von: parseFloat(item.gia_von || 0)
    }));

    await ChiTietKiemKe.bulkCreate(chiTietData, { transaction });

    // Cập nhật tổng số sản phẩm trong phiếu kiểm kê
    const totalProducts = await ChiTietKiemKe.count({
      where: { phieu_kiem_ke_id: id },
      transaction
    });

    await stockCheck.update({
      tong_san_pham: totalProducts,
      nguoi_cap_nhap: req.user?.username || 'system',
      ngay_cap_nhap: new Date()
    }, { transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: `Đã thêm ${inventoryItems.length} sản phẩm vào phiếu kiểm kê`,
      data: {
        added_count: inventoryItems.length,
        total_products: totalProducts
      }
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Xóa sản phẩm khỏi phiếu kiểm kê
 */
const removeProductFromStockCheck = async (req, res) => {
  const { id, itemId } = req.params;

  const transaction = await sequelize.transaction();

  try {
    const stockCheck = await PhieuKiemKe.findByPk(id, { transaction });
    if (!stockCheck) {
      throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
    }

    if (stockCheck.trang_thai === 'hoan_thanh') {
      throw new AppError('Không thể xóa sản phẩm khỏi phiếu kiểm kê đã hoàn thành', 400);
    }

    const detail = await ChiTietKiemKe.findOne({
      where: { id: itemId, phieu_kiem_ke_id: id },
      transaction
    });

    if (!detail) {
      throw new AppError('Không tìm thấy sản phẩm trong phiếu kiểm kê', 404);
    }

    // Không cho phép xóa nếu đã kiểm kê
    if (detail.so_luong_thuc_te !== null) {
      throw new AppError('Không thể xóa sản phẩm đã được kiểm kê', 400);
    }

    await detail.destroy({ transaction });

    // Cập nhật tổng số sản phẩm
    const totalProducts = await ChiTietKiemKe.count({
      where: { phieu_kiem_ke_id: id },
      transaction
    });

    await stockCheck.update({
      tong_san_pham: totalProducts,
      nguoi_cap_nhap: req.user?.username || 'system',
      ngay_cap_nhap: new Date()
    }, { transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Đã xóa sản phẩm khỏi phiếu kiểm kê',
      data: {
        total_products: totalProducts
      }
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Lấy chi tiết sản phẩm cần kiểm kê
 */
const getStockCheckDetails = async (req, res) => {
  const { id } = req.params;
  const { page = 1, limit = 20, search = '' } = req.query;
  const offset = (page - 1) * limit;

  const stockCheck = await PhieuKiemKe.findByPk(id);
  if (!stockCheck) {
    throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
  }

  // Build where clause for search
  const whereClause = { phieu_kiem_ke_id: id };

  const includeWhere = {};
  if (search) {
    includeWhere[Op.or] = [
      { ten_phien_ban: { [Op.like]: `%${search}%` } },
      { ma: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows } = await ChiTietKiemKe.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        where: Object.keys(includeWhere).length > 0 ? includeWhere : undefined,
        include: [
          {
            model: SanPham,
            as: 'sanPham',
            attributes: ['id', 'ten', 'ma']
          }
        ]
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['id', 'ASC']]
  });

  // Format data for frontend
  const detailsData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.id,
      phien_ban_id: data.phien_ban_san_pham_id,
      ten_san_pham: data.phienBanSanPham?.sanPham?.ten || 'N/A',
      ten_phien_ban: data.phienBanSanPham?.ten_phien_ban || 'N/A',
      ma_sku: data.phienBanSanPham?.ma || 'N/A',
      anh: data.phienBanSanPham?.anh || null,
      don_vi_tinh: data.phienBanSanPham?.don_vi_tinh || 'Cái',
      so_luong_he_thong: data.so_luong_he_thong,
      so_luong_thuc_te: data.so_luong_thuc_te,
      so_luong_lech: data.so_luong_lech,
      gia_von: parseFloat(data.gia_von || 0),
      gia_tri_lech: parseFloat(data.gia_tri_lech || 0),
      trang_thai: data.trang_thai,
      ghi_chu: data.ghi_chu,
      nguoi_kiem: data.nguoi_kiem,
      ngay_kiem: data.ngay_kiem
    };
  });

  res.json({
    success: true,
    data: {
      stockCheck: {
        id: stockCheck.id,
        ma_kiem_ke: stockCheck.ma_kiem_ke,
        ten_kiem_ke: stockCheck.ten_kiem_ke,
        kho_hang_id: stockCheck.kho_hang_id,
        trang_thai: stockCheck.trang_thai,
        ngay_bat_dau: stockCheck.ngay_bat_dau,
        ngay_ket_thuc: stockCheck.ngay_ket_thuc,
        nguoi_kiem_ke: stockCheck.nguoi_kiem_ke,
        tong_san_pham: stockCheck.tong_san_pham,
        san_pham_da_kiem: stockCheck.san_pham_da_kiem,
        san_pham_lech: stockCheck.san_pham_lech,
        gia_tri_lech: parseFloat(stockCheck.gia_tri_lech || 0),
        ghi_chu: stockCheck.ghi_chu,
        nguoi_tao: stockCheck.nguoi_tao,
        nguoi_cap_nhap: stockCheck.nguoi_cap_nhap,
        ngay_tao: stockCheck.ngay_tao,
        ngay_cap_nhap: stockCheck.ngay_cap_nhap
      },
      details: detailsData,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    }
  });
};

/**
 * Cập nhật số lượng thực tế cho sản phẩm
 */
const updateStockCheckItem = async (req, res) => {
  const { id, itemId } = req.params;
  const { so_luong_thuc_te, ghi_chu } = req.body;

  if (so_luong_thuc_te === undefined || so_luong_thuc_te === null) {
    throw new AppError('Số lượng thực tế là bắt buộc', 400);
  }

  if (so_luong_thuc_te < 0) {
    throw new AppError('Số lượng thực tế không thể âm', 400);
  }

  const transaction = await sequelize.transaction();

  try {
    // Tìm phiếu kiểm kê
    const stockCheck = await PhieuKiemKe.findByPk(id, { transaction });
    if (!stockCheck) {
      throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
    }

    // Chỉ cho phép cập nhật khi phiếu đang thực hiện
    if (stockCheck.trang_thai === 'hoan_thanh') {
      throw new AppError('Không thể cập nhật phiếu kiểm kê đã hoàn thành', 400);
    }

    // Cập nhật trạng thái phiếu thành "đang thực hiện" nếu chưa
    if (stockCheck.trang_thai === 'ke_hoach') {
      await stockCheck.update({ trang_thai: 'dang_thuc_hien' }, { transaction });
    }

    // Tìm chi tiết kiểm kê
    const detail = await ChiTietKiemKe.findOne({
      where: { id: itemId, phieu_kiem_ke_id: id },
      transaction
    });

    if (!detail) {
      throw new AppError('Không tìm thấy sản phẩm trong phiếu kiểm kê', 404);
    }

    // Tính toán chênh lệch
    const so_luong_lech = parseInt(so_luong_thuc_te) - detail.so_luong_he_thong;
    const gia_tri_lech = so_luong_lech * parseFloat(detail.gia_von || 0);
    const trang_thai = so_luong_lech === 0 ? 'da_kiem' : 'co_lech';

    // Cập nhật chi tiết
    await detail.update({
      so_luong_thuc_te: parseInt(so_luong_thuc_te),
      so_luong_lech,
      gia_tri_lech,
      trang_thai,
      ghi_chu: ghi_chu || '',
      nguoi_kiem: req.user?.username || 'system',
      ngay_kiem: new Date()
    }, { transaction });

    // Cập nhật thống kê phiếu kiểm kê
    const allDetails = await ChiTietKiemKe.findAll({
      where: { phieu_kiem_ke_id: id },
      transaction
    });

    const san_pham_da_kiem = allDetails.filter(d => d.so_luong_thuc_te !== null).length;
    const san_pham_lech = allDetails.filter(d => d.so_luong_lech !== 0 && d.so_luong_thuc_te !== null).length;
    const gia_tri_lech_tong = allDetails
      .filter(d => d.so_luong_thuc_te !== null)
      .reduce((sum, d) => sum + parseFloat(d.gia_tri_lech || 0), 0);

    await stockCheck.update({
      san_pham_da_kiem,
      san_pham_lech,
      gia_tri_lech: gia_tri_lech_tong,
      nguoi_cap_nhap: req.user?.username || 'system',
      ngay_cap_nhap: new Date()
    }, { transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Cập nhật số lượng thành công',
      data: {
        so_luong_thuc_te: parseInt(so_luong_thuc_te),
        so_luong_lech,
        gia_tri_lech,
        trang_thai,
        san_pham_da_kiem,
        tien_do: Math.round((san_pham_da_kiem / stockCheck.tong_san_pham) * 100)
      }
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Hoàn thành kiểm kê
 */
const completeStockCheck = async (req, res) => {
  const { id } = req.params;

  const transaction = await sequelize.transaction();

  try {
    const stockCheck = await PhieuKiemKe.findByPk(id, { transaction });
    if (!stockCheck) {
      throw new AppError('Không tìm thấy phiếu kiểm kê', 404);
    }

    if (stockCheck.trang_thai === 'hoan_thanh') {
      throw new AppError('Phiếu kiểm kê đã được hoàn thành', 400);
    }

    // Kiểm tra xem đã kiểm kê hết chưa
    const totalItems = await ChiTietKiemKe.count({
      where: { phieu_kiem_ke_id: id },
      transaction
    });

    const checkedItems = await ChiTietKiemKe.count({
      where: {
        phieu_kiem_ke_id: id,
        so_luong_thuc_te: { [Op.not]: null }
      },
      transaction
    });

    if (checkedItems < totalItems) {
      throw new AppError(`Chưa kiểm kê hết sản phẩm. Đã kiểm: ${checkedItems}/${totalItems}`, 400);
    }

    // Cập nhật trạng thái hoàn thành
    await stockCheck.update({
      trang_thai: 'hoan_thanh',
      ngay_ket_thuc: new Date(),
      nguoi_cap_nhap: req.user?.username || 'system',
      ngay_cap_nhap: new Date()
    }, { transaction });

    // Tạo lịch sử kho cho các sản phẩm có chênh lệch
    const itemsWithDifference = await ChiTietKiemKe.findAll({
      where: {
        phieu_kiem_ke_id: id,
        so_luong_lech: { [Op.not]: 0 }
      },
      include: [
        {
          model: PhienBanSanPham,
          as: 'phienBanSanPham'
        }
      ],
      transaction
    });

    for (const item of itemsWithDifference) {
      // Lấy tồn kho hiện tại
      const inventory = await TonKhoPhienBan.findOne({
        where: {
          phien_ban_san_pham_id: item.phien_ban_san_pham_id,
          kho_hang_id: stockCheck.kho_hang_id
        },
        transaction
      });

      if (inventory) {
        const oldStock = inventory.so_luong_ton || 0;
        const newStock = item.so_luong_thuc_te;
        const changeAmount = item.so_luong_lech;

        // Cập nhật tồn kho
        await inventory.update({
          so_luong_ton: newStock,
          ngay_cap_nhap: new Date()
        }, { transaction });

        // Tạo lịch sử kho
        await LichSuKho.create({
          phien_ban_san_pham_id: item.phien_ban_san_pham_id,
          kho_hang_id: stockCheck.kho_hang_id,
          phieu_kiem_ke_id: stockCheck.id,
          loai_giao_dich: changeAmount > 0 ? 'kiem_ke_tang' : 'kiem_ke_giam',
          so_luong_truoc: oldStock,
          so_luong_thay_doi: changeAmount,
          so_luong_sau: newStock,
          gia_von: item.gia_von,
          ly_do: 'Điều chỉnh sau kiểm kê',
          nguoi_thuc_hien: req.user?.username || 'system',
          ghi_chu: `Kiểm kê ${stockCheck.ma_kiem_ke}: ${item.ghi_chu || ''}`,
          nguoi_tao: req.user?.username || 'system',
          nguoi_cap_nhap: req.user?.username || 'system'
        }, { transaction });
      }
    }

    await transaction.commit();

    res.json({
      success: true,
      message: 'Hoàn thành kiểm kê thành công',
      data: {
        id: stockCheck.id,
        trang_thai: 'hoan_thanh',
        ngay_ket_thuc: new Date(),
        items_adjusted: itemsWithDifference.length
      }
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

module.exports = {
  getStockChecks,
  getStockCheck,
  createStockCheck,
  updateStockCheck,
  deleteStockCheck,
  getAvailableProducts,
  addProductToStockCheck,
  removeProductFromStockCheck,
  getStockCheckDetails,
  updateStockCheckItem,
  completeStockCheck
};
