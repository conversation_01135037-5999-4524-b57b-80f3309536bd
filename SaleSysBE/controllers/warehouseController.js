const { <PERSON><PERSON><PERSON><PERSON>, Ton<PERSON><PERSON>, PhienBan<PERSON>an<PERSON>, SanPham, LichSuKho, sequelize } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

/**
 * L<PERSON>y danh sách kho hàng
 */
const getWarehouses = async (req, res) => {
  console.log('🏪 getWarehouses Controller Called');
  console.log('- Query params:', req.query);
  const { page = 1, limit = 10, search, status } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = {};
  
  // Search by name or address
  if (search) {
    whereClause[Op.or] = [
      { ten_kho: { [Op.like]: `%${search}%` } },
      { dia_chi: { [Op.like]: `%${search}%` } }
    ];
  }

  // Filter by status
  if (status) {
    whereClause.trang_thai = status;
  }

  const { count, rows } = await KhoHang.findAndCountAll({
    where: whereClause,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_tao', 'DESC']]
  });

  // Calculate statistics for each warehouse
  const warehousesWithStats = await Promise.all(
    rows.map(async (warehouse) => {
      const warehouseData = warehouse.toJSON();
      
      // Count total products in warehouse
      const totalProducts = await TonKhoPhienBan.count({
        where: { kho_hang_id: warehouse.id }
      });
      
      // Calculate total stock
      const stockSum = await TonKhoPhienBan.sum('so_luong_ton', {
        where: { kho_hang_id: warehouse.id }
      });

      return {
        ...warehouseData,
        tong_san_pham: totalProducts,
        tong_ton_kho: stockSum || 0,
        trang_thai: warehouseData.trang_thai === 1 ? 'active' : 'inactive'
      };
    })
  );

  const response = {
    success: true,
    data: warehousesWithStats,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  };

  console.log('📤 Sending response:');
  console.log('- Success:', response.success);
  console.log('- Data count:', response.data.length);
  console.log('- First warehouse:', response.data[0]);

  res.json(response);
};

/**
 * Lấy thông tin chi tiết kho hàng
 */
const getWarehouse = async (req, res) => {
  const { id } = req.params;

  const warehouse = await KhoHang.findByPk(id);
  if (!warehouse) {
    throw new AppError('Không tìm thấy kho hàng', 404);
  }

  const warehouseData = warehouse.toJSON();
  
  // Get statistics
  const totalProducts = await TonKhoPhienBan.count({
    where: { kho_hang_id: id }
  });
  
  const stockSum = await TonKhoPhienBan.sum('so_luong_ton', {
    where: { kho_hang_id: id }
  });

  res.json({
    success: true,
    data: {
      ...warehouseData,
      tong_san_pham: totalProducts,
      tong_ton_kho: stockSum || 0,
      trang_thai: warehouseData.trang_thai === 1 ? 'active' : 'inactive'
    }
  });
};

/**
 * Tạo kho hàng mới
 */
const createWarehouse = async (req, res) => {
  const { ten_kho, dia_chi, mo_ta } = req.body;

  // Validate required fields
  if (!ten_kho || !dia_chi) {
    throw new AppError('Tên kho và địa chỉ là bắt buộc', 400);
  }

  // Check if warehouse name already exists
  const existingWarehouse = await KhoHang.findOne({
    where: { ten_kho }
  });

  if (existingWarehouse) {
    throw new AppError('Tên kho hàng đã tồn tại', 400);
  }

  const warehouse = await KhoHang.create({
    ten_kho,
    dia_chi,
    mo_ta,
    trang_thai: 1, // active by default
    nguoi_tao: req.user?.username || 'system'
  });

  res.status(201).json({
    success: true,
    message: 'Tạo kho hàng thành công',
    data: {
      ...warehouse.toJSON(),
      tong_san_pham: 0,
      tong_ton_kho: 0,
      trang_thai: 'active'
    }
  });
};

/**
 * Cập nhật kho hàng
 */
const updateWarehouse = async (req, res) => {
  const { id } = req.params;
  const { ten_kho, dia_chi, mo_ta, trang_thai } = req.body;

  const warehouse = await KhoHang.findByPk(id);
  if (!warehouse) {
    throw new AppError('Không tìm thấy kho hàng', 404);
  }

  // Check if new name already exists (excluding current warehouse)
  if (ten_kho && ten_kho !== warehouse.ten_kho) {
    const existingWarehouse = await KhoHang.findOne({
      where: { 
        ten_kho,
        id: { [Op.ne]: id }
      }
    });

    if (existingWarehouse) {
      throw new AppError('Tên kho hàng đã tồn tại', 400);
    }
  }

  await warehouse.update({
    ten_kho: ten_kho || warehouse.ten_kho,
    dia_chi: dia_chi || warehouse.dia_chi,
    mo_ta: mo_ta !== undefined ? mo_ta : warehouse.mo_ta,
    trang_thai: trang_thai !== undefined ? (trang_thai === 'active' ? 1 : 0) : warehouse.trang_thai,
    nguoi_cap_nhap: req.user?.username || 'system'
  });

  // Get updated data with statistics
  const totalProducts = await TonKhoPhienBan.count({
    where: { kho_hang_id: id }
  });
  
  const stockSum = await TonKhoPhienBan.sum('so_luong_ton', {
    where: { kho_hang_id: id }
  });

  res.json({
    success: true,
    message: 'Cập nhật kho hàng thành công',
    data: {
      ...warehouse.toJSON(),
      tong_san_pham: totalProducts,
      tong_ton_kho: stockSum || 0,
      trang_thai: warehouse.trang_thai === 1 ? 'active' : 'inactive'
    }
  });
};

/**
 * Xóa kho hàng
 */
const deleteWarehouse = async (req, res) => {
  const { id } = req.params;

  const warehouse = await KhoHang.findByPk(id);
  if (!warehouse) {
    throw new AppError('Không tìm thấy kho hàng', 404);
  }

  // Check if warehouse has inventory
  const hasInventory = await TonKhoPhienBan.count({
    where: { kho_hang_id: id }
  });

  if (hasInventory > 0) {
    throw new AppError('Không thể xóa kho hàng có tồn kho', 400);
  }

  await warehouse.destroy();

  res.json({
    success: true,
    message: 'Xóa kho hàng thành công'
  });
};

/**
 * Lấy danh sách tồn kho
 */
const getInventory = async (req, res) => {
  const { page = 1, limit = 10, warehouse_id, search, stock_filter } = req.query;
  const offset = (page - 1) * limit;

  // Build search conditions for PhienBanSanPham
  const variantWhereClause = {};
  if (search) {
    variantWhereClause[Op.or] = [
      { '$sanPham.ten$': { [Op.like]: `%${search}%` } },
      { '$sanPham.ma$': { [Op.like]: `%${search}%` } },
      { ten_phien_ban: { [Op.like]: `%${search}%` } },
      { ma: { [Op.like]: `%${search}%` } }
    ];
  }

  // Get all product variants with their inventory data
  const { count, rows } = await PhienBanSanPham.findAndCountAll({
    where: variantWhereClause,
    include: [
      {
        model: SanPham,
        as: 'sanPham',
        attributes: ['ten', 'ma']
      },
      {
        model: TonKhoPhienBan,
        as: 'tonKhoList',
        include: [
          {
            model: KhoHang,
            as: 'khoHang',
            attributes: ['ten_kho']
          }
        ],
        where: warehouse_id ? { kho_hang_id: warehouse_id } : {},
        required: false // LEFT JOIN to include variants without inventory
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_cap_nhap', 'DESC']]
  });

  // Format data for frontend - each variant as separate product
  const inventoryData = [];

  rows.forEach(variant => {
    const variantData = variant.toJSON();
    const sanPham = variantData.sanPham?.ten || 'N/A';
    const phienBan = variantData.ten_phien_ban || 'N/A';
    const maSanPham = variantData.sanPham?.ma || '';

    if (variantData.tonKhoList && variantData.tonKhoList.length > 0) {
      // Variant has inventory records
      variantData.tonKhoList.forEach(inventory => {
        // Apply stock filter if specified
        const tonKho = inventory.so_luong_ton || 0;
        const tonKhoToiThieu = inventory.so_luong_toi_thieu || 0;

        let includeRecord = true;
        if (stock_filter === 'low') {
          includeRecord = tonKho <= tonKhoToiThieu && tonKho > 0;
        } else if (stock_filter === 'out') {
          includeRecord = tonKho === 0;
        } else if (stock_filter === 'normal') {
          includeRecord = tonKho > tonKhoToiThieu;
        }

        if (includeRecord) {
          inventoryData.push({
            id: inventory.id,
            san_pham: sanPham,
            phien_ban: phienBan,
            ma_sku: variantData.ma || 'N/A',
            ma_san_pham_goc: maSanPham,
            phien_ban_id: variantData.id,
            san_pham_id: variantData.san_pham_id,
            kho_id: inventory.kho_hang_id,
            ten_kho: inventory.khoHang?.ten_kho || 'N/A',
            ton_kho: tonKho,
            ton_kho_toi_thieu: tonKhoToiThieu,
            gia_von: inventory.gia_von || 0,
            gia_ban: variantData.gia_le || 0,
            gia_buon: variantData.gia_buon || 0,
            gia_nhap: variantData.gia_nhap || 0,
            don_vi_tinh: variantData.don_vi_tinh || 'cái',
            ngay_cap_nhat: inventory.ngay_cap_nhap || inventory.updatedAt,
            ten_day_du: `${sanPham} - ${phienBan}`,
            loai_hang: 'phien_ban'
          });
        }
      });
    } else {
      // Variant has no inventory records - show with 0 stock
      // Only include if no stock filter or filter allows 0 stock
      let includeRecord = true;
      if (stock_filter === 'low' || stock_filter === 'normal') {
        includeRecord = false; // Don't show variants without inventory for these filters
      }

      if (includeRecord) {
        inventoryData.push({
          id: `variant_${variantData.id}`, // Unique ID for variants without inventory
          san_pham: sanPham,
          phien_ban: phienBan,
          ma_sku: variantData.ma || 'N/A',
          ma_san_pham_goc: maSanPham,
          phien_ban_id: variantData.id,
          san_pham_id: variantData.san_pham_id,
          kho_id: null,
          ten_kho: 'Chưa khởi tạo',
          ton_kho: 0,
          ton_kho_toi_thieu: 0,
          gia_von: 0,
          gia_ban: variantData.gia_le || 0,
          gia_buon: variantData.gia_buon || 0,
          gia_nhap: variantData.gia_nhap || 0,
          don_vi_tinh: variantData.don_vi_tinh || 'cái',
          ngay_cap_nhat: variantData.ngay_cap_nhap || variantData.updatedAt,
          ten_day_du: `${sanPham} - ${phienBan}`,
          loai_hang: 'phien_ban',
          chua_khoi_tao: true // Flag to indicate this variant needs inventory initialization
        });
      }
    }
  });

  // Calculate statistics for variants
  const totalVariants = inventoryData.length;
  const lowStockVariants = inventoryData.filter(item =>
    item.ton_kho <= item.ton_kho_toi_thieu && item.ton_kho > 0
  ).length;
  const outOfStockVariants = inventoryData.filter(item => item.ton_kho === 0).length;
  const totalValue = inventoryData.reduce((sum, item) => sum + (item.ton_kho * item.gia_von), 0);
  const variantsNeedInit = inventoryData.filter(item => item.chua_khoi_tao).length;

  res.json({
    success: true,
    data: inventoryData,
    statistics: {
      total_variants: totalVariants,
      low_stock_variants: lowStockVariants,
      out_of_stock_variants: outOfStockVariants,
      variants_need_init: variantsNeedInit,
      total_inventory_value: totalValue,
      unique_products: [...new Set(inventoryData.map(item => item.san_pham_id))].length
    },
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Điều chỉnh tồn kho
 */
const adjustInventory = async (req, res) => {
  try {
    console.log('🔧 adjustInventory called with data:', req.body);

    const {
      inventory_id,
      phien_ban_id,
      kho_id,
      adjustment_type,
      adjustment_value,
      new_stock,
      new_min_stock,
      new_cost_price,
      reason
    } = req.body;

    if (!reason) {
      throw new AppError('Lý do điều chỉnh là bắt buộc', 400);
    }

    let inventory;
    let oldStock = 0;
    let oldMinStock = 0;

    console.log('📋 Processing inventory_id:', inventory_id);
    console.log('📋 phien_ban_id:', phien_ban_id, 'kho_id:', kho_id);

    // Handle variants that don't have inventory records yet (inventory_id starts with "variant_")
    if (inventory_id && inventory_id.toString().startsWith('variant_')) {
      console.log('🆕 Handling variant without inventory record');

      if (!phien_ban_id) {
        throw new AppError('Thiếu thông tin phiên bản sản phẩm', 400);
      }

      if (!kho_id) {
        throw new AppError('Vui lòng chọn kho hàng để khởi tạo tồn kho', 400);
      }

      // Try to find existing inventory record
      console.log('🔍 Searching for existing inventory record...');
      inventory = await TonKhoPhienBan.findOne({
        where: {
          phien_ban_san_pham_id: phien_ban_id,
          kho_hang_id: kho_id
        }
      });

      if (!inventory) {
        console.log('➕ Creating new inventory record');
        try {
          inventory = await TonKhoPhienBan.create({
            phien_ban_san_pham_id: phien_ban_id,
            kho_hang_id: kho_id,
            so_luong_ton: new_stock || 0,
            so_luong_toi_thieu: new_min_stock || 0,
            gia_von: new_cost_price || 0,
            nguoi_tao: req.user?.username || 'system'
          });
          console.log('✅ New inventory created with ID:', inventory.id);
        } catch (createError) {
          console.log('⚠️ Error creating inventory, checking if it exists now:', createError.message);

          // Có thể record đã được tạo bởi request khác, thử tìm lại
          inventory = await TonKhoPhienBan.findOne({
            where: {
              phien_ban_san_pham_id: phien_ban_id,
              kho_hang_id: kho_id
            }
          });

          if (!inventory) {
            throw createError; // Nếu vẫn không tìm thấy thì throw lỗi gốc
          }

          console.log('📦 Found inventory after create error:', inventory.id);
          oldStock = inventory.so_luong_ton;
          oldMinStock = inventory.so_luong_toi_thieu;
        }
      } else {
        console.log('📦 Found existing inventory record:', inventory.id);
        oldStock = inventory.so_luong_ton;
        oldMinStock = inventory.so_luong_toi_thieu;
      }
    } else {
      console.log('📦 Handling existing inventory record');

      // Handle existing inventory records
      if (!inventory_id) {
        throw new AppError('ID tồn kho là bắt buộc', 400);
      }

      inventory = await TonKhoPhienBan.findByPk(inventory_id);
      if (!inventory) {
        throw new AppError('Không tìm thấy tồn kho', 404);
      }

      console.log('📦 Found inventory record:', inventory.id);
      oldStock = inventory.so_luong_ton;
      oldMinStock = inventory.so_luong_toi_thieu;
    }

    // Update inventory
    console.log('🔄 Updating inventory with data:', {
      new_stock,
      new_min_stock,
      new_cost_price,
      oldStock,
      oldMinStock
    });

    const updateData = {
      nguoi_cap_nhap: req.user?.username || 'system'
    };

    if (new_stock !== undefined) {
      updateData.so_luong_ton = new_stock;
    }

    if (new_min_stock !== undefined) {
      updateData.so_luong_toi_thieu = new_min_stock;
    }

    if (new_cost_price !== undefined) {
      updateData.gia_von = new_cost_price;
    }

    console.log('📝 Update data:', updateData);
    await inventory.update(updateData);
    console.log('✅ Inventory updated successfully');

    // Create history record if stock changed
    if (new_stock !== undefined && new_stock !== oldStock) {
      console.log('📚 Creating history record...');
      try {
        await LichSuKho.create({
          phien_ban_san_pham_id: inventory.phien_ban_san_pham_id,
          kho_hang_id: inventory.kho_hang_id,
          loai_giao_dich: 'dieu_chinh',
          so_luong_truoc: oldStock,
          so_luong_thay_doi: new_stock - oldStock,
          so_luong_sau: new_stock,
          ly_do: reason,
          nguoi_thuc_hien: req.user?.username || 'system'
        });
        console.log('✅ History record created');
      } catch (historyError) {
        console.error('⚠️ Error creating history record:', historyError.message);
        // Không throw error vì inventory đã được update thành công
      }
    }

    const responseData = {
      id: inventory.id,
      old_stock: oldStock,
      new_stock: new_stock !== undefined ? new_stock : oldStock,
      old_min_stock: oldMinStock,
      new_min_stock: new_min_stock !== undefined ? new_min_stock : oldMinStock,
      difference: new_stock !== undefined ? new_stock - oldStock : 0
    };

    console.log('📤 Sending response:', responseData);

    res.json({
      success: true,
      message: 'Điều chỉnh tồn kho thành công',
      data: responseData
    });

  } catch (error) {
    console.error('❌ Error in adjustInventory:', error);
    console.error('❌ Error stack:', error.stack);

    // Send detailed error for debugging
    res.status(500).json({
      success: false,
      message: 'Database error',
      detail: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Lấy lịch sử xuất nhập kho
 */
const getStockMovements = async (req, res) => {
  const { page = 1, limit = 10, warehouse_id, type, start_date, end_date } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = {};

  // Filter by warehouse
  if (warehouse_id) {
    whereClause.kho_hang_id = warehouse_id;
  }

  // Filter by movement type
  if (type) {
    whereClause.loai_giao_dich = type;
  }

  // Filter by date range
  if (start_date && end_date) {
    whereClause.ngay_thuc_hien = {
      [Op.between]: [start_date, end_date]
    };
  }

  const { count, rows } = await LichSuKho.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        include: [
          {
            model: SanPham,
            as: 'sanPham',
            attributes: ['ten', 'ma']
          }
        ]
      },
      {
        model: KhoHang,
        as: 'khoHang',
        attributes: ['ten_kho']
      },
      {
        model: KhoHang,
        as: 'khoChuyenDen',
        attributes: ['ten_kho'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_thuc_hien', 'DESC']]
  });

  // Format data for frontend
  const movementsData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.id,
      loai: data.loai_giao_dich,
      san_pham: data.phienBanSanPham?.sanPham?.ten || 'N/A',
      ma_sku: data.phienBanSanPham?.ma || 'N/A',
      kho_id: data.kho_hang_id,
      ten_kho: data.khoHang?.ten_kho || 'N/A',
      kho_dich: data.khoChuyenDen?.ten_kho || null,
      so_luong: data.so_luong_thay_doi,
      gia_von: data.gia_von || 0,
      tong_gia_tri: (data.so_luong_thay_doi || 0) * (data.gia_von || 0),
      ly_do: data.ly_do || '',
      nguoi_thuc_hien: data.nguoi_thuc_hien,
      ngay_thuc_hien: data.ngay_thuc_hien,
      ghi_chu: data.ghi_chu || ''
    };
  });

  res.json({
    success: true,
    data: movementsData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Tạo giao dịch xuất nhập kho
 */
const createStockMovement = async (req, res) => {
  const {
    san_pham_id,
    kho_id,
    so_luong,
    gia_von,
    ly_do,
    ghi_chu,
    loai_giao_dich = 'nhap' // nhap hoặc xuat
  } = req.body;

  if (!san_pham_id || !kho_id || !so_luong || !ly_do) {
    throw new AppError('Thiếu thông tin bắt buộc', 400);
  }

  // Find or create inventory record
  let inventory = await TonKhoPhienBan.findOne({
    where: {
      phien_ban_san_pham_id: san_pham_id,
      kho_hang_id: kho_id
    }
  });

  const oldStock = inventory ? inventory.so_luong_ton : 0;
  const changeAmount = loai_giao_dich === 'xuat' ? -Math.abs(so_luong) : Math.abs(so_luong);
  const newStock = Math.max(0, oldStock + changeAmount);

  if (loai_giao_dich === 'xuat' && newStock < 0) {
    throw new AppError('Số lượng xuất vượt quá tồn kho', 400);
  }

  if (!inventory) {
    // Create new inventory record
    inventory = await TonKhoPhienBan.create({
      phien_ban_san_pham_id: san_pham_id,
      kho_hang_id: kho_id,
      so_luong_ton: newStock,
      gia_von: gia_von || 0,
      so_luong_toi_thieu: 0,
      nguoi_tao: req.user?.username || 'system'
    });
  } else {
    // Update existing inventory
    await inventory.update({
      so_luong_ton: newStock,
      gia_von: gia_von || inventory.gia_von,
      nguoi_cap_nhap: req.user?.username || 'system'
    });
  }

  // Create history record
  await LichSuKho.create({
    phien_ban_san_pham_id: san_pham_id,
    kho_hang_id: kho_id,
    loai_giao_dich: loai_giao_dich,
    so_luong_truoc: oldStock,
    so_luong_thay_doi: changeAmount,
    so_luong_sau: newStock,
    gia_von: gia_von || 0,
    ly_do: ly_do,
    nguoi_thuc_hien: req.user?.username || 'system',
    ghi_chu: ghi_chu || '',
    nguoi_tao: req.user?.username || 'system',
    nguoi_cap_nhap: req.user?.username || 'system'
  });

  res.status(201).json({
    success: true,
    message: `${loai_giao_dich === 'nhap' ? 'Nhập' : 'Xuất'} kho thành công`,
    data: {
      old_stock: oldStock,
      change_amount: changeAmount,
      new_stock: newStock
    }
  });
};

/**
 * Lấy lịch sử kho theo sản phẩm
 */
const getProductStockHistory = async (req, res) => {
  const { productId } = req.params;
  const { page = 1, limit = 20, warehouse_id, type, start_date, end_date } = req.query;
  const offset = (page - 1) * limit;

  // Tìm tất cả phiên bản của sản phẩm
  const productVariants = await PhienBanSanPham.findAll({
    where: { san_pham_id: productId },
    attributes: ['id']
  });

  if (productVariants.length === 0) {
    return res.json({
      success: true,
      data: [],
      pagination: {
        total: 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: 0
      }
    });
  }

  const variantIds = productVariants.map(v => v.id);
  const whereClause = {
    phien_ban_san_pham_id: {
      [Op.in]: variantIds
    }
  };

  // Filter by warehouse
  if (warehouse_id) {
    whereClause.kho_hang_id = warehouse_id;
  }

  // Filter by movement type
  if (type) {
    whereClause.loai_giao_dich = type;
  }

  // Filter by date range
  if (start_date && end_date) {
    whereClause.ngay_thuc_hien = {
      [Op.between]: [start_date, end_date]
    };
  }

  const { count, rows } = await LichSuKho.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        include: [
          {
            model: SanPham,
            as: 'sanPham',
            attributes: ['ten', 'ma']
          }
        ]
      },
      {
        model: KhoHang,
        as: 'khoHang',
        attributes: ['ten_kho']
      },
      {
        model: KhoHang,
        as: 'khoChuyenDen',
        attributes: ['ten_kho'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_thuc_hien', 'DESC']]
  });

  // Format data for frontend
  const historyData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.id,
      loai: data.loai_giao_dich,
      san_pham: data.phienBanSanPham?.sanPham?.ten || 'N/A',
      phien_ban: data.phienBanSanPham?.ten_phien_ban || 'Mặc định',
      ma_sku: data.phienBanSanPham?.ma || 'N/A',
      kho_id: data.kho_hang_id,
      ten_kho: data.khoHang?.ten_kho || 'N/A',
      kho_dich: data.khoChuyenDen?.ten_kho || null,
      so_luong_truoc: data.so_luong_truoc,
      so_luong_thay_doi: data.so_luong_thay_doi,
      so_luong_sau: data.so_luong_sau,
      gia_von: data.gia_von || 0,
      tong_gia_tri: Math.abs(data.so_luong_thay_doi || 0) * (data.gia_von || 0),
      ly_do: data.ly_do || '',
      nguoi_thuc_hien: data.nguoi_thuc_hien,
      ngay_thuc_hien: data.ngay_thuc_hien,
      ghi_chu: data.ghi_chu || ''
    };
  });

  res.json({
    success: true,
    data: historyData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

/**
 * Lấy lịch sử kho theo phiên bản sản phẩm
 */
const getVariantStockHistory = async (req, res) => {
  const { variantId } = req.params;
  const { page = 1, limit = 20, warehouse_id, type, start_date, end_date } = req.query;
  const offset = (page - 1) * limit;

  const whereClause = {
    phien_ban_san_pham_id: variantId
  };

  // Filter by warehouse
  if (warehouse_id) {
    whereClause.kho_hang_id = warehouse_id;
  }

  // Filter by movement type
  if (type) {
    whereClause.loai_giao_dich = type;
  }

  // Filter by date range
  if (start_date && end_date) {
    whereClause.ngay_thuc_hien = {
      [Op.between]: [start_date, end_date]
    };
  }

  const { count, rows } = await LichSuKho.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: PhienBanSanPham,
        as: 'phienBanSanPham',
        include: [
          {
            model: SanPham,
            as: 'sanPham',
            attributes: ['ten', 'ma']
          }
        ]
      },
      {
        model: KhoHang,
        as: 'khoHang',
        attributes: ['ten_kho']
      },
      {
        model: KhoHang,
        as: 'khoChuyenDen',
        attributes: ['ten_kho'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['ngay_thuc_hien', 'DESC']]
  });

  // Format data for frontend
  const historyData = rows.map(item => {
    const data = item.toJSON();
    return {
      id: data.id,
      loai: data.loai_giao_dich,
      san_pham: data.phienBanSanPham?.sanPham?.ten || 'N/A',
      phien_ban: data.phienBanSanPham?.ten_phien_ban || 'Mặc định',
      ma_sku: data.phienBanSanPham?.ma || 'N/A',
      kho_id: data.kho_hang_id,
      ten_kho: data.khoHang?.ten_kho || 'N/A',
      kho_dich: data.khoChuyenDen?.ten_kho || null,
      so_luong_truoc: data.so_luong_truoc,
      so_luong_thay_doi: data.so_luong_thay_doi,
      so_luong_sau: data.so_luong_sau,
      gia_von: data.gia_von || 0,
      tong_gia_tri: Math.abs(data.so_luong_thay_doi || 0) * (data.gia_von || 0),
      ly_do: data.ly_do || '',
      nguoi_thuc_hien: data.nguoi_thuc_hien,
      ngay_thuc_hien: data.ngay_thuc_hien,
      ghi_chu: data.ghi_chu || ''
    };
  });

  res.json({
    success: true,
    data: historyData,
    pagination: {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    }
  });
};

module.exports = {
  getWarehouses,
  getWarehouse,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  getInventory,
  adjustInventory,
  getStockMovements,
  createStockMovement,
  getProductStockHistory,
  getVariantStockHistory
};
