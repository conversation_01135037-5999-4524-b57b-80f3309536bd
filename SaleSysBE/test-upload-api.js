const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testUploadAPI() {
  try {
    console.log('🧪 Testing upload API...');

    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0xBC, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Create form data
    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test.png',
      contentType: 'image/png'
    });
    formData.append('folder', 'test');
    formData.append('width', '400');
    formData.append('height', '300');

    console.log('📤 Sending request to http://localhost:5000/api/upload/single');

    const response = await fetch('http://localhost:5000/api/upload/single', {
      method: 'POST',
      body: formData,
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('📄 Response body:', responseText);

    if (response.ok) {
      const data = JSON.parse(responseText);
      if (data.success) {
        console.log('✅ Upload successful!');
        console.log('🖼️ Image URL:', data.data.url);
        console.log('🔗 Public ID:', data.data.public_id);
      } else {
        console.log('❌ Upload failed:', data.message);
      }
    } else {
      console.log('❌ HTTP Error:', response.status, responseText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test
testUploadAPI();
