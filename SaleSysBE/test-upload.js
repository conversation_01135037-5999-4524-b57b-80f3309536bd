const { uploadImage, uploadMultipleImages } = require('./utils/cloudinaryUpload');
const fs = require('fs');
const path = require('path');

// Test upload từ URL
async function testUploadFromUrl() {
  console.log('🧪 Test upload từ URL...');
  
  const result = await uploadImage(
    'https://res.cloudinary.com/demo/image/upload/getting-started/shoes.jpg',
    {
      folder: 'test',
      width: 800,
      height: 600,
      public_id: `test_url_${Date.now()}`
    }
  );

  if (result.success) {
    console.log('✅ Upload thành công!');
    console.log('📸 URL gốc:', result.data.url);
    console.log('🎨 URL tối ưu:', result.data.optimized_url);
    console.log('🖼️ Thumbnail:', result.data.thumbnail_url);
  } else {
    console.log('❌ Upload thất bại:', result.error);
  }
}

// Test upload từ file local (nếu có)
async function testUploadFromFile() {
  console.log('\n🧪 Test upload từ file local...');
  
  // Tạo file test đơn giản (1x1 pixel PNG)
  const testImageBuffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
    0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
    0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0xBC, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  const result = await uploadImage(testImageBuffer, {
    folder: 'test',
    width: 400,
    height: 300,
    public_id: `test_buffer_${Date.now()}`
  });

  if (result.success) {
    console.log('✅ Upload buffer thành công!');
    console.log('📸 URL:', result.data.url);
    console.log('📏 Kích thước:', `${result.data.width}x${result.data.height}`);
  } else {
    console.log('❌ Upload buffer thất bại:', result.error);
  }
}

// Test upload multiple
async function testUploadMultiple() {
  console.log('\n🧪 Test upload multiple images...');
  
  const testImages = [
    'https://res.cloudinary.com/demo/image/upload/getting-started/shoes.jpg',
    'https://picsum.photos/800/600?random=1',
    'https://picsum.photos/800/600?random=2'
  ];

  const result = await uploadMultipleImages(testImages, {
    folder: 'test-multiple',
    width: 600,
    height: 400
  });

  if (result.success) {
    console.log(`✅ Upload thành công ${result.uploaded}/${testImages.length} ảnh!`);
    result.data.forEach((img, index) => {
      console.log(`📸 Ảnh ${index + 1}: ${img.url}`);
    });
  } else {
    console.log('❌ Upload multiple thất bại:', result.error);
    if (result.errors) {
      result.errors.forEach((error, index) => {
        console.log(`❌ Lỗi ảnh ${index + 1}: ${error}`);
      });
    }
  }
}

// Chạy tất cả test
async function runAllTests() {
  try {
    console.log('🚀 Bắt đầu test Cloudinary upload...\n');
    
    await testUploadFromUrl();
    await testUploadFromFile();
    await testUploadMultiple();
    
    console.log('\n✅ Hoàn thành tất cả test!');
    
  } catch (error) {
    console.error('❌ Lỗi trong quá trình test:', error);
  } finally {
    process.exit(0);
  }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testUploadFromUrl,
  testUploadFromFile,
  testUploadMultiple
};
