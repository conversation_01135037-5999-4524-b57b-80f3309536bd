'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('quyen', [
      // Quyền người dùng
      { ma_quyen: 'XEM_NGUOI_DUNG', ten_quyen: 'Xem người dùng', mo_ta: 'Quyền xem danh sách và thông tin người dùng' },
      { ma_quyen: 'THEM_NGUOI_DUNG', ten_quyen: 'Thêm người dùng', mo_ta: 'Quyền tạo người dùng mới' },
      { ma_quyen: 'SUA_NGUOI_DUNG', ten_quyen: 'Sửa người dùng', mo_ta: 'Quyền chỉnh sửa thông tin người dùng' },
      { ma_quyen: 'XOA_NGUOI_DUNG', ten_quyen: 'X<PERSON>a người dùng', mo_ta: 'Quyền xóa người dùng' },

      // Quyền vai trò
      { ma_quyen: 'XEM_VAI_TRO', ten_quyen: 'Xem vai trò', mo_ta: 'Quyền xem danh sách vai trò' },
      { ma_quyen: 'THEM_VAI_TRO', ten_quyen: 'Thêm vai trò', mo_ta: 'Quyền tạo vai trò mới' },
      { ma_quyen: 'SUA_VAI_TRO', ten_quyen: 'Sửa vai trò', mo_ta: 'Quyền chỉnh sửa vai trò' },
      { ma_quyen: 'XOA_VAI_TRO', ten_quyen: 'Xóa vai trò', mo_ta: 'Quyền xóa vai trò' },

      // Quyền phân quyền
      { ma_quyen: 'XEM_QUYEN', ten_quyen: 'Xem quyền', mo_ta: 'Quyền xem danh sách quyền' },
      { ma_quyen: 'THEM_QUYEN', ten_quyen: 'Thêm quyền', mo_ta: 'Quyền tạo quyền mới' },
      { ma_quyen: 'SUA_QUYEN', ten_quyen: 'Sửa quyền', mo_ta: 'Quyền chỉnh sửa quyền' },
      { ma_quyen: 'XOA_QUYEN', ten_quyen: 'Xóa quyền', mo_ta: 'Quyền xóa quyền' },

      // Quyền sản phẩm
      { ma_quyen: 'XEM_SAN_PHAM', ten_quyen: 'Xem sản phẩm', mo_ta: 'Quyền xem danh sách sản phẩm' },
      { ma_quyen: 'THEM_SAN_PHAM', ten_quyen: 'Thêm sản phẩm', mo_ta: 'Quyền tạo sản phẩm mới' },
      { ma_quyen: 'SUA_SAN_PHAM', ten_quyen: 'Sửa sản phẩm', mo_ta: 'Quyền chỉnh sửa sản phẩm' },
      { ma_quyen: 'XOA_SAN_PHAM', ten_quyen: 'Xóa sản phẩm', mo_ta: 'Quyền xóa sản phẩm' },

      // Quyền đơn hàng
      { ma_quyen: 'XEM_DON_HANG', ten_quyen: 'Xem đơn hàng', mo_ta: 'Quyền xem danh sách đơn hàng' },
      { ma_quyen: 'THEM_DON_HANG', ten_quyen: 'Thêm đơn hàng', mo_ta: 'Quyền tạo đơn hàng mới' },
      { ma_quyen: 'SUA_DON_HANG', ten_quyen: 'Sửa đơn hàng', mo_ta: 'Quyền chỉnh sửa đơn hàng' },
      { ma_quyen: 'XOA_DON_HANG', ten_quyen: 'Xóa đơn hàng', mo_ta: 'Quyền xóa đơn hàng' },

      // Quyền khách hàng
      { ma_quyen: 'XEM_KHACH_HANG', ten_quyen: 'Xem khách hàng', mo_ta: 'Quyền xem danh sách khách hàng' },
      { ma_quyen: 'THEM_KHACH_HANG', ten_quyen: 'Thêm khách hàng', mo_ta: 'Quyền tạo khách hàng mới' },
      { ma_quyen: 'SUA_KHACH_HANG', ten_quyen: 'Sửa khách hàng', mo_ta: 'Quyền chỉnh sửa thông tin khách hàng' },
      { ma_quyen: 'XOA_KHACH_HANG', ten_quyen: 'Xóa khách hàng', mo_ta: 'Quyền xóa khách hàng' },

      // Quyền kho hàng
      { ma_quyen: 'XEM_KHO_HANG', ten_quyen: 'Xem kho hàng', mo_ta: 'Quyền xem thông tin kho hàng' },
      { ma_quyen: 'THEM_KHO_HANG', ten_quyen: 'Thêm kho hàng', mo_ta: 'Quyền tạo kho hàng mới' },
      { ma_quyen: 'SUA_KHO_HANG', ten_quyen: 'Sửa kho hàng', mo_ta: 'Quyền chỉnh sửa thông tin kho hàng' },
      { ma_quyen: 'XOA_KHO_HANG', ten_quyen: 'Xóa kho hàng', mo_ta: 'Quyền xóa kho hàng' },

      // Quyền báo cáo
      { ma_quyen: 'XEM_BAO_CAO', ten_quyen: 'Xem báo cáo', mo_ta: 'Quyền xem các báo cáo' },
      { ma_quyen: 'XUAT_BAO_CAO', ten_quyen: 'Xuất báo cáo', mo_ta: 'Quyền xuất báo cáo' },

      // Quyền cấu hình hệ thống
      { ma_quyen: 'CAU_HINH_HE_THONG', ten_quyen: 'Cấu hình hệ thống', mo_ta: 'Quyền cấu hình các thiết lập hệ thống' }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('quyen', null, {});
  }
};
