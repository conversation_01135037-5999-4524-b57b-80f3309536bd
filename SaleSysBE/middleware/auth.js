const jwt = require('jsonwebtoken');
const { <PERSON>uo<PERSON><PERSON><PERSON>, Vai<PERSON><PERSON>, <PERSON>uy<PERSON> } = require('../models');

/**
 * Middleware xác thực JWT token
 */
const authenticateToken = async (req, res, next) => {
  try {
    console.log('🔑 Auth Token Debug:');
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    console.log('- Auth header:', authHeader ? 'Present' : 'Missing');
    console.log('- Token:', token ? 'Present' : 'Missing');

    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      });
    }

    console.log('- Verifying token...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('- Token decoded:', decoded);
    
    // <PERSON><PERSON>y thông tin user từ database
    console.log('- Finding user with ID:', decoded.userId);
    const user = await NguoiDung.findByPk(decoded.userId, {
      include: [
        {
          model: VaiTro,
          as: 'vaiTroList',
          include: [
            {
              model: Quyen,
              as: 'quyenList'
            }
          ]
        }
      ]
    });

    console.log('- User found:', user ? 'Yes' : 'No');
    if (!user) {
      console.log('❌ User not found in database');
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.trang_thai !== 'dang_giao_dich') {
      return res.status(401).json({ 
        success: false, 
        message: 'User account is inactive' 
      });
    }

    // Gắn thông tin user vào request
    req.user = user;
    req.userId = user.id;

    // Tạo danh sách quyền của user
    const permissions = [];
    user.vaiTroList?.forEach(role => {
      role.quyenList?.forEach(permission => {
        if (!permissions.includes(permission.ma_quyen)) {
          permissions.push(permission.ma_quyen);
        }
      });
    });
    req.userPermissions = permissions;

    console.log('- User type:', user.loai_nguoi_dung);
    console.log('- User permissions:', permissions);
    console.log('✅ Authentication successful');
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token' 
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Token expired' 
      });
    }
    
    console.error('Auth middleware error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

/**
 * Middleware kiểm tra quyền truy cập
 * @param {string|string[]} requiredPermissions - Quyền cần thiết
 */
const requirePermission = (requiredPermissions) => {
  return (req, res, next) => {
    try {
      console.log('🔐 Permission Check Debug:');
      console.log('- Required permissions:', requiredPermissions);
      console.log('- User:', req.user ? 'Present' : 'Missing');
      console.log('- User type:', req.user?.loai_nguoi_dung);
      console.log('- User permissions:', req.userPermissions);

      if (!req.user) {
        console.log('❌ No user found');
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // Chuyển đổi thành mảng nếu là string
      const permissions = Array.isArray(requiredPermissions)
        ? requiredPermissions
        : [requiredPermissions];

      // Kiểm tra xem user có ít nhất một trong các quyền cần thiết
      const hasPermission = permissions.some(permission =>
        req.userPermissions?.includes(permission)
      );

      // Admin có tất cả quyền
      const isAdmin = req.user.loai_nguoi_dung === 'admin';

      console.log('- Has permission:', hasPermission);
      console.log('- Is admin:', isAdmin);

      if (!hasPermission && !isAdmin) {
        console.log('❌ Permission denied');
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          required: permissions,
          current: req.userPermissions
        });
      }

      console.log('✅ Permission granted');
      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  };
};

/**
 * Middleware kiểm tra vai trò
 * @param {string|string[]} requiredRoles - Vai trò cần thiết
 */
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      // Chuyển đổi thành mảng nếu là string
      const roles = Array.isArray(requiredRoles) 
        ? requiredRoles 
        : [requiredRoles];

      // Lấy danh sách vai trò của user
      const userRoles = req.user.vaiTroList?.map(role => role.ma_vai_tro) || [];

      // Kiểm tra xem user có ít nhất một trong các vai trò cần thiết
      const hasRole = roles.some(role => userRoles.includes(role));

      // Admin có tất cả vai trò
      const isAdmin = req.user.loai_nguoi_dung === 'admin';

      if (!hasRole && !isAdmin) {
        return res.status(403).json({ 
          success: false, 
          message: 'Insufficient role',
          required: roles,
          current: userRoles
        });
      }

      next();
    } catch (error) {
      console.error('Role middleware error:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  };
};

/**
 * Middleware chỉ cho phép admin
 */
const requireAdmin = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }

    if (req.user.loai_nguoi_dung !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Admin access required' 
      });
    }

    next();
  } catch (error) {
    console.error('Admin middleware error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

module.exports = {
  authenticateToken,
  requirePermission,
  requireRole,
  requireAdmin
};
