# Debug: Cân bằng kho không hoạt động

## 🔍 Các bước debug:

### **Bước 1: Kiểm tra danh sách phiếu kiểm kê**
1. Vào: http://localhost:5174/warehouses/stock-check
2. ✅ **FIXED**: Đã sửa để lấy dữ liệu thật từ API
3. Kiểm tra có phiếu kiểm kê nào không

### **Bước 2: Tạo phiếu kiểm kê mới**
1. Vào: http://localhost:5174/warehouses/check/create
2. Tạo phiếu với 1 sản phẩm
3. Ghi nhận ID phiếu được tạo

### **Bước 3: Kiểm kê sản phẩm**
1. Vào chi tiết phiếu: http://localhost:5174/warehouses/check/{ID}
2. Click "Kiể<PERSON> kê" cho sản phẩm
3. <PERSON>hập số lượng thực tế khác với hệ thống
4. **Kiểm tra console logs** khi click "Lưu"
5. Xem API call có thành công không

### **Bước 4: Cân bằng kho**
1. Khi progress = 100%
2. Click "Cân bằng kho"
3. **Kiểm tra console logs** khi click
4. Xem API call có thành công không

### **Bước 5: Kiểm tra kết quả**
1. Vào inventory: http://localhost:5174/warehouses/inventory
2. Kiểm tra tồn kho có thay đổi không

## 🔧 Debug logs đã thêm:

### **Frontend logs:**
```javascript
// Khi lưu số lượng thực tế
console.log('🔄 Saving item:', { stockCheckId: id, itemId: item.id, values });

// Khi cân bằng kho
console.log('🏁 Completing stock check:', id);
console.log('📊 Current stock check data:', stockCheck);
console.log('📋 Current details:', details);
```

### **Các điểm cần kiểm tra:**

1. **API updateStockCheckItem có được gọi không?**
   - Network tab → PUT `/api/warehouses/stock-checks/{id}/items/{itemId}`
   - Response có success: true không?

2. **API completeStockCheck có được gọi không?**
   - Network tab → POST `/api/warehouses/stock-checks/{id}/complete`
   - Response có success: true không?

3. **Cache invalidation có hoạt động không?**
   - React Query DevTools
   - Xem inventory cache có được invalidate không

4. **Database có được cập nhật không?**
   - Kiểm tra bảng `TonKhoPhienBan`
   - Kiểm tra bảng `LichSuKho`

## 🚨 Các lỗi có thể gặp:

### **1. Lỗi validation:**
```
"Số lượng thực tế là bắt buộc"
"Số lượng thực tế không thể âm"
```

### **2. Lỗi trạng thái:**
```
"Không thể cập nhật phiếu kiểm kê đã hoàn thành"
"Chưa kiểm kê hết sản phẩm"
```

### **3. Lỗi quyền:**
```
"Không có quyền truy cập"
```

### **4. Lỗi dữ liệu:**
```
"Không tìm thấy phiếu kiểm kê"
"Không tìm thấy sản phẩm trong phiếu kiểm kê"
```

## 🎯 Expected behavior:

1. **Lưu số lượng thực tế:**
   - ✅ API call thành công
   - ✅ Chênh lệch được tính đúng
   - ✅ Progress được cập nhật
   - ✅ UI hiển thị số liệu mới

2. **Cân bằng kho:**
   - ✅ API call thành công
   - ✅ Tồn kho được cập nhật
   - ✅ Lịch sử kho được tạo
   - ✅ Cache được invalidate
   - ✅ UI hiển thị "Đã cân bằng"

## 🔧 Nếu vẫn không hoạt động:

1. **Kiểm tra backend logs**
2. **Kiểm tra database trực tiếp**
3. **Kiểm tra React Query DevTools**
4. **Kiểm tra Network tab**
5. **Kiểm tra Console errors**
